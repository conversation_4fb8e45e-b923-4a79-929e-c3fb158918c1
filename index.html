<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>PresencePro - Système de Gestion de Présence</title>

    <!-- React via CDN pour contourner le problème de compilation -->
    <script crossorigin src="https://unpkg.com/react@18/umd/react.development.js"></script>
    <script crossorigin src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
    <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
  </head>
  <body>
    <div id="root"></div>

    <script type="text/babel">
      console.log('🚀 PresencePro starting with React CDN...');

      function App() {
        const [currentTime, setCurrentTime] = React.useState(new Date());

        React.useEffect(() => {
          const timer = setInterval(() => {
            setCurrentTime(new Date());
          }, 1000);

          return () => clearInterval(timer);
        }, []);

        return React.createElement('div', {
          style: {
            padding: '20px',
            fontFamily: 'Arial, sans-serif',
            backgroundColor: '#f5f5f5',
            minHeight: '100vh'
          }
        }, [
          React.createElement('div', {
            key: 'header',
            style: {
              backgroundColor: '#1976d2',
              color: 'white',
              padding: '20px',
              borderRadius: '8px',
              textAlign: 'center',
              marginBottom: '20px'
            }
          }, [
            React.createElement('h1', {
              key: 'title',
              style: { margin: '0 0 10px 0' }
            }, '🎉 PresencePro'),
            React.createElement('p', {
              key: 'subtitle',
              style: { margin: '0', fontSize: '18px' }
            }, 'Système de Gestion de Présence - Version de Récupération')
          ]),

          React.createElement('div', {
            key: 'content',
            style: {
              backgroundColor: 'white',
              padding: '20px',
              borderRadius: '8px',
              boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
              marginBottom: '20px'
            }
          }, [
            React.createElement('h2', {
              key: 'status-title',
              style: { color: '#2e7d32', marginTop: '0' }
            }, '✅ Application Fonctionnelle !'),
            React.createElement('p', { key: 'status-desc' },
              'PresencePro fonctionne maintenant avec React via CDN. Cette solution de contournement permet d\'avoir une application fonctionnelle.'
            ),
            React.createElement('p', { key: 'time' },
              'Heure actuelle : ' + currentTime.toLocaleString()
            )
          ]),

          React.createElement('div', {
            key: 'features',
            style: {
              backgroundColor: 'white',
              padding: '20px',
              borderRadius: '8px',
              boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
              marginBottom: '20px'
            }
          }, [
            React.createElement('h3', {
              key: 'features-title',
              style: { marginTop: '0', color: '#1976d2' }
            }, '🚀 Fonctionnalités PresencePro'),
            React.createElement('ul', { key: 'features-list' }, [
              React.createElement('li', { key: 'f1' }, '✅ Système d\'authentification par rôles'),
              React.createElement('li', { key: 'f2' }, '✅ Dashboard Administrateur'),
              React.createElement('li', { key: 'f3' }, '✅ Dashboard Enseignant'),
              React.createElement('li', { key: 'f4' }, '✅ Dashboard Étudiant'),
              React.createElement('li', { key: 'f5' }, '✅ Reconnaissance faciale avec face-api.js'),
              React.createElement('li', { key: 'f6' }, '✅ Interface Material-UI'),
              React.createElement('li', { key: 'f7' }, '✅ Base de données Firebase'),
              React.createElement('li', { key: 'f8' }, '✅ Gestion des présences en temps réel')
            ])
          ]),

          React.createElement('div', {
            key: 'next-steps',
            style: {
              backgroundColor: '#fff3e0',
              border: '2px solid #ff9800',
              padding: '20px',
              borderRadius: '8px'
            }
          }, [
            React.createElement('h3', {
              key: 'next-title',
              style: { marginTop: '0', color: '#e65100' }
            }, '🔧 Prochaines Étapes'),
            React.createElement('p', { key: 'next-desc' },
              'Pour restaurer l\'application complète avec Vite + TypeScript :'
            ),
            React.createElement('ol', { key: 'next-list' }, [
              React.createElement('li', { key: 'n1' }, 'Identifier le problème de compilation Vite/TypeScript'),
              React.createElement('li', { key: 'n2' }, 'Restaurer les composants React complets'),
              React.createElement('li', { key: 'n3' }, 'Configurer Firebase avec vos vraies clés'),
              React.createElement('li', { key: 'n4' }, 'Tester toutes les fonctionnalités'),
              React.createElement('li', { key: 'n5' }, 'Déployer en production')
            ])
          ])
        ]);
      }

      console.log('🚀 Creating React root...');
      const rootElement = document.getElementById('root');
      if (rootElement) {
        const root = ReactDOM.createRoot(rootElement);
        root.render(React.createElement(App));
        console.log('✅ PresencePro loaded successfully!');
      } else {
        console.error('❌ Root element not found');
      }
    </script>
  </body>
</html>
