<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>React Test Direct</title>
    <script crossorigin src="https://unpkg.com/react@18/umd/react.development.js"></script>
    <script crossorigin src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
    <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
</head>
<body>
    <div id="react-root"></div>
    
    <script type="text/babel">
        function App() {
            console.log('🚀 React App component rendering via CDN!');
            
            return React.createElement('div', {
                style: { 
                    padding: '20px', 
                    backgroundColor: '#e8f5e8', 
                    border: '2px solid #4caf50',
                    borderRadius: '8px',
                    margin: '20px',
                    textAlign: 'center'
                }
            }, [
                React.createElement('h1', { 
                    key: 'title',
                    style: { color: '#2e7d32' } 
                }, '🎉 React Fonctionne via CDN !'),
                React.createElement('p', { 
                    key: 'desc' 
                }, 'Si vous voyez cette page, React fonctionne parfaitement.'),
                React.createElement('p', { 
                    key: 'timestamp' 
                }, 'Timestamp: ' + new Date().toLocaleString()),
                React.createElement('div', {
                    key: 'diagnostic',
                    style: {
                        backgroundColor: '#f3e5f5',
                        padding: '15px',
                        borderRadius: '4px',
                        marginTop: '20px'
                    }
                }, [
                    React.createElement('h3', { key: 'diag-title' }, 'Diagnostic :'),
                    React.createElement('p', { key: 'diag-text' }, 'React via CDN fonctionne, le problème est donc dans la configuration Vite/TypeScript.')
                ])
            ]);
        }

        console.log('🚀 Starting React CDN test...');
        
        const rootElement = document.getElementById('react-root');
        if (rootElement) {
            console.log('✅ Root element found');
            const root = ReactDOM.createRoot(rootElement);
            root.render(React.createElement(App));
            console.log('✅ React app rendered successfully via CDN!');
        } else {
            console.error('❌ Root element not found');
        }
    </script>
</body>
</html>
