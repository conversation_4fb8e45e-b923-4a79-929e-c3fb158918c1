<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Debug PresencePro</title>
  </head>
  <body>
    <div id="root"></div>
    
    <script>
      console.log('🚀 Debug script starting...');
      
      // Test 1: Vérifier que le DOM est chargé
      document.addEventListener('DOMContentLoaded', function() {
        console.log('✅ DOM loaded');
        
        const root = document.getElementById('root');
        if (root) {
          console.log('✅ Root element found');
          
          // Test 2: Ajouter du contenu simple
          root.innerHTML = `
            <div style="padding: 20px; background-color: #e8f5e8; border: 2px solid #4caf50; border-radius: 8px; margin: 20px; text-align: center;">
              <h1 style="color: #2e7d32;">🎉 JavaScript Fonctionne !</h1>
              <p>Si vous voyez ce texte, JavaScript s'exécute correctement.</p>
              <p>Timestamp: ${new Date().toLocaleString()}</p>
              <div style="background-color: #f3e5f5; padding: 15px; border-radius: 4px; margin-top: 20px;">
                <h3>Diagnostic :</h3>
                <p>✅ Serveur Vite : OK</p>
                <p>✅ HTML : OK</p>
                <p>✅ JavaScript : OK</p>
                <p>❓ React : À tester</p>
              </div>
            </div>
          `;
          
          console.log('✅ Content added to root element');
        } else {
          console.error('❌ Root element not found');
          document.body.innerHTML = '<h1 style="color: red;">ERROR: Root element not found!</h1>';
        }
      });
      
      // Test 3: Vérifier que le script s'exécute immédiatement
      console.log('✅ Script executed immediately');
    </script>
  </body>
</html>
