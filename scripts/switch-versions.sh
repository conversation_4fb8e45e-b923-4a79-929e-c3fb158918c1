#!/bin/bash

# Script pour basculer entre les différentes versions de PresencePro

echo "🔧 PresencePro - Gestionnaire de Versions"
echo "========================================"

# Sauvegarder la version actuelle
backup_current() {
    echo "📦 Sauvegarde de la version actuelle..."
    cp index.html index.html.backup.$(date +%Y%m%d_%H%M%S)
    echo "✅ Sauvegarde créée"
}

# Version React CDN (fonctionnelle)
use_cdn_version() {
    echo "🚀 Activation de la version React CDN..."
    if [ -f "index.html.cdn" ]; then
        cp index.html.cdn index.html
    else
        echo "⚠️  Fichier index.html.cdn non trouvé, utilisation de la version actuelle"
    fi
    echo "✅ Version CDN activée"
}

# Version Vite + TypeScript (à réparer)
use_vite_version() {
    echo "🔧 Activation de la version Vite + TypeScript..."
    cat > index.html << 'EOF'
<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>PresencePro - Vite + React + TS</title>
  </head>
  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>
EOF
    echo "✅ Version Vite activée"
}

# Version JSX (test)
use_jsx_version() {
    echo "📝 Activation de la version JSX..."
    cat > index.html << 'EOF'
<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>PresencePro - JSX Test</title>
  </head>
  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.jsx"></script>
  </body>
</html>
EOF
    echo "✅ Version JSX activée"
}

# Afficher le menu
show_menu() {
    echo ""
    echo "Choisissez une version :"
    echo "1) React CDN (✅ Fonctionnelle)"
    echo "2) Vite + TypeScript (🔧 À réparer)"
    echo "3) JSX Test (📝 Test)"
    echo "4) Sauvegarder version actuelle"
    echo "5) Quitter"
    echo ""
}

# Sauvegarder la version CDN actuelle
cp index.html index.html.cdn

# Menu principal
while true; do
    show_menu
    read -p "Votre choix (1-5): " choice
    
    case $choice in
        1)
            backup_current
            use_cdn_version
            echo "🎉 Version React CDN activée - L'application devrait fonctionner"
            ;;
        2)
            backup_current
            use_vite_version
            echo "⚠️  Version Vite activée - Peut ne pas fonctionner (page blanche)"
            ;;
        3)
            backup_current
            use_jsx_version
            echo "📝 Version JSX activée - Pour tests de compilation"
            ;;
        4)
            backup_current
            echo "📦 Version actuelle sauvegardée"
            ;;
        5)
            echo "👋 Au revoir !"
            break
            ;;
        *)
            echo "❌ Choix invalide, veuillez réessayer"
            ;;
    esac
    
    echo ""
    echo "Redémarrez le serveur avec 'npm run dev' pour voir les changements"
    echo "Appuyez sur Entrée pour continuer..."
    read
done
