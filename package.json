{"name": "presencepro", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview", "download-models": "./scripts/download-models.sh", "setup": "npm install && npm run download-models"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@mui/icons-material": "^7.1.1", "@mui/material": "^7.1.1", "@mui/x-date-pickers": "^8.5.1", "@types/react-router-dom": "^5.3.3", "dayjs": "^1.11.13", "face-api.js": "^0.20.0", "firebase": "^11.9.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-router-dom": "^7.6.2"}, "devDependencies": {"@eslint/js": "^9.28.0", "@types/react": "^18.3.23", "@types/react-dom": "^18.3.7", "@vitejs/plugin-react": "^4.5.1", "eslint": "^9.28.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.2.0", "typescript": "~5.8.3", "typescript-eslint": "^8.34.0", "vite": "^6.3.5"}}