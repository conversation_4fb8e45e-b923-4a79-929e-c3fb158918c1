function App() {
  // Ajoutons des logs pour debug
  console.log('🚀 App component is rendering!');

  // Test si le problème vient des variables d'environnement
  try {
    console.log('Environment variables:', import.meta.env);
  } catch (error) {
    console.error('Error accessing environment variables:', error);
  }

  return (
    <div>
      <h1>🎉 REACT FONCTIONNE !</h1>
      <p>Si vous voyez ce texte, React s'exécute correctement.</p>
    </div>
  );
}

export default App;
