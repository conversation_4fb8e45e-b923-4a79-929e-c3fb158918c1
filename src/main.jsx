import { StrictMode } from 'react'
import { createRoot } from 'react-dom/client'
import App from './App.jsx'

console.log('🚀 main.jsx is executing!');

// Vérifier que l'élément root existe
const rootElement = document.getElementById('root');
console.log('Root element found:', rootElement);

if (!rootElement) {
  console.error('❌ Root element not found!');
  document.body.innerHTML = '<h1 style="color: red;">ERROR: Root element not found!</h1>';
} else {
  console.log('✅ Root element found, creating React root...');
  
  try {
    const root = createRoot(rootElement);
    console.log('✅ React root created, rendering App...');
    
    root.render(
      <StrictMode>
        <App />
      </StrictMode>
    );
    
    console.log('✅ App rendered successfully!');
  } catch (error) {
    console.error('❌ Error creating or rendering React app:', error);
    document.body.innerHTML = `<h1 style="color: red;">ERROR: ${error}</h1>`;
  }
}
