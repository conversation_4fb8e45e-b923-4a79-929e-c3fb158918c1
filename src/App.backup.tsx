import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import { CssBaseline, Box, Typography, Card, CardContent, Alert } from '@mui/material';
import SetupInstructions from './components/setup/SetupInstructions';


// Thème Material-UI
const theme = createTheme({
  palette: {
    primary: {
      main: '#1976d2',
    },
    secondary: {
      main: '#dc004e',
    },
  },
});

function App() {
  // Vérifier si Firebase est configuré
  const isFirebaseConfigured = import.meta.env.VITE_FIREBASE_API_KEY &&
                               import.meta.env.VITE_FIREBASE_API_KEY !== "demo-api-key";

  // Si Firebase n'est pas configuré, afficher les instructions
  if (!isFirebaseConfigured) {
    return (
      <ThemeProvider theme={theme}>
        <CssBaseline />
        <SetupInstructions />
      </ThemeProvider>
    );
  }

  // Si Firebase est configuré, afficher un message de succès pour l'instant
  return (
    <ThemeProvider theme={theme}>
      <CssBaseline />
      <Box sx={{ maxWidth: 800, mx: 'auto', p: 3 }}>
        <Typography variant="h4" gutterBottom align="center" color="primary">
          🎉 PresencePro - Configuration Réussie !
        </Typography>

        <Alert severity="success" sx={{ mb: 3 }}>
          <Typography variant="body1">
            ✅ Firebase est configuré ! L'application est prête à être utilisée.
          </Typography>
        </Alert>

        <Card>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              Configuration détectée :
            </Typography>
            <Box sx={{ bgcolor: 'grey.100', p: 2, borderRadius: 1, fontFamily: 'monospace' }}>
              <Typography variant="body2">
                VITE_FIREBASE_API_KEY: {import.meta.env.VITE_FIREBASE_API_KEY}<br />
                VITE_FIREBASE_PROJECT_ID: {import.meta.env.VITE_FIREBASE_PROJECT_ID}<br />
                VITE_FIREBASE_AUTH_DOMAIN: {import.meta.env.VITE_FIREBASE_AUTH_DOMAIN}
              </Typography>
            </Box>
          </CardContent>
        </Card>

        <Alert severity="info" sx={{ mt: 3 }}>
          <Typography variant="body1">
            <strong>Pour activer l'application complète :</strong><br />
            1. Restaurez le code complet dans App.tsx<br />
            2. Initialisez les données de démonstration<br />
            3. Connectez-vous avec les comptes de test
          </Typography>
        </Alert>
      </Box>
    </ThemeProvider>
  );
}

export default App;
