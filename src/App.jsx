function App() {
  console.log('🚀 App.jsx component is rendering!');
  
  return (
    <div style={{ 
      padding: '20px', 
      backgroundColor: '#e8f5e8', 
      border: '2px solid #4caf50',
      borderRadius: '8px',
      margin: '20px',
      textAlign: 'center'
    }}>
      <h1 style={{ color: '#2e7d32' }}>🎉 React Fonctionne avec JSX !</h1>
      <p>Si vous voyez ce texte, React + JSX fonctionne parfaitement.</p>
      <p>Timestamp: {new Date().toLocaleString()}</p>
      <div style={{
        backgroundColor: '#f3e5f5',
        padding: '15px',
        borderRadius: '4px',
        marginTop: '20px'
      }}>
        <h3>Diagnostic :</h3>
        <p>React avec JSX fonctionne, le problème était peut-être dans TypeScript.</p>
      </div>
    </div>
  );
}

export default App;
