{"version": 3, "file": "slice_gpu.js", "sourceRoot": "", "sources": ["../../../src/backends/webgl/slice_gpu.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;;GAeG;;AAIH,qDAAoD;AAEpD;IASE,sBAAY,QAAkB;QAR9B,kBAAa,GAAG,CAAC,QAAQ,CAAC,CAAC;QASzB,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC;QAC5B,IAAI,CAAC,IAAI,GAAG,QAAQ,CAAC,MAAM,CAAC;QAE5B,IAAM,KAAK,GAAG,mCAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC3C,IAAM,WAAW,GAAG,uBAAqB,IAAI,CAAC,IAAI,OAAI,CAAC;QACvD,IAAM,YAAY,GAAG,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAE1C,IAAI,IAAY,CAAC;QACjB,IAAM,QAAQ,GAAG,QAAQ,CAAC,GAAG,CAAC,UAAC,CAAC,EAAE,CAAC;YACjC,OAAO,eAAa,MAAM,CAAC,CAAC,CAAC,iBAAY,CAAC,mBAAc,MAAM,CAAC,CAAC,CAAC,MAAG,CAAC;QACvE,CAAC,CAAC,CAAC;QACH,IAAI,GAAG,eACD,KAAK,6BACL,KAAK,8CACL,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,aACtB,CAAC;QACJ,IAAI,CAAC,QAAQ,GAAG,aACZ,WAAW,uCAET,IAAI,sCACgB,YAAY,uBAErC,CAAC;IACJ,CAAC;IAED,yCAAkB,GAAlB,UAAmB,KAAe;QAAlC,iBAiBC;QAhBC,IAAI,KAAK,CAAC,MAAM,KAAK,IAAI,CAAC,IAAI,EAAE;YAC9B,MAAM,KAAK,CACP,eAAa,IAAI,CAAC,IAAI,qCAAkC;iBACxD,sBAAoB,KAAK,CAAC,MAAM,MAAG,CAAA,CAAC,CAAC;SAC1C;QACD,OAAO,UAAC,KAAmB,EAAE,YAA0B;YACrD,IAAI,KAAI,CAAC,QAAQ,IAAI,IAAI,EAAE;gBACzB,KAAI,CAAC,QAAQ,GAAG,KAAK,CAAC,yBAAyB,CAAC,YAAY,EAAE,OAAO,CAAC,CAAC;gBACvE,IAAI,KAAI,CAAC,QAAQ,IAAI,IAAI,EAAE;oBACzB,qEAAqE;oBACrE,eAAe;oBACf,OAAO;iBACR;aACF;YACD,KAAK,CAAC,EAAE,CAAC,UAAU,CAAC,KAAI,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;QAC5C,CAAC,CAAC;IACJ,CAAC;IACH,mBAAC;AAAD,CAAC,AArDD,IAqDC;AArDY,oCAAY;AAuDzB,IAAM,MAAM,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;AAE9C,SAAS,SAAS,CAAC,IAAY;IAC7B,IAAI,IAAI,KAAK,CAAC,EAAE;QACd,OAAO,WAAW,CAAC;KACpB;SAAM,IAAI,IAAI,IAAI,CAAC,EAAE;QACpB,OAAO,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,GAAG,CAAC,UAAA,CAAC,IAAI,OAAA,YAAY,GAAG,CAAC,EAAhB,CAAgB,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;KACnE;SAAM;QACL,MAAM,KAAK,CAAC,sBAAoB,IAAI,0BAAuB,CAAC,CAAC;KAC9D;AACH,CAAC"}