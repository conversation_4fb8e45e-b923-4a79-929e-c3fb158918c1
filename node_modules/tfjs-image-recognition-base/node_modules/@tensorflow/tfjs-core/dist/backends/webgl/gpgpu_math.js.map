{"version": 3, "file": "gpgpu_math.js", "sourceRoot": "", "sources": ["../../../src/backends/webgl/gpgpu_math.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;;GAeG;;AAEH,iDAAsC;AAGtC,iCAAmC;AAGnC,mDAAqD;AA2CrD,SAAgB,cAAc,CAC1B,KAAmB,EAAE,OAAqB,EAAE,MAAoB,EAChE,MAAkB;IACpB,IAAM,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC;IAClC,IAAM,UAAU,GAAgB,MAAM,CAAC,GAAG,CAAC,UAAC,KAAK,EAAE,CAAC;QAClD,IAAM,SAAS,GAAc;YAC3B,YAAY,EAAE,KAAK,CAAC,KAAK;YACzB,QAAQ,EAAE,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,QAAQ;YACzD,SAAS,EAAE,KAAK,CAAC,SAAS;YAC1B,QAAQ,EAAE,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,QAAQ;YAC1D,UAAU,EAAE,IAAI;SACjB,CAAC;QACF,IAAI,KAAK,CAAC,OAAO,IAAI,IAAI,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,IAAI,IAAI;YACpD,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,UAAU,GAAG,CAAC,EAAE;YACtC,SAAS,CAAC,UAAU,GAAG,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,UAAU,CAAC;SACvD;QACD,OAAO,EAAC,IAAI,EAAE,OAAO,CAAC,aAAa,CAAC,CAAC,CAAC,EAAE,SAAS,WAAA,EAAC,CAAC;IACrD,CAAC,CAAC,CAAC;IACH,IAAM,YAAY,GAAG,UAAU,CAAC,GAAG,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,SAAS,EAAX,CAAW,CAAC,CAAC;IACtD,IAAM,YAAY,GAAc;QAC9B,YAAY,EAAE,MAAM,CAAC,KAAK;QAC1B,QAAQ,EAAE,MAAM,CAAC,OAAO,CAAC,QAAQ;QACjC,SAAS,EAAE,KAAK;QAChB,QAAQ,EAAE,MAAM,CAAC,OAAO,CAAC,QAAQ;QACjC,UAAU,EAAE,IAAI;KACjB,CAAC;IACF,IAAM,MAAM,GAAG,eAAe,CAAC,UAAU,CACrC,UAAU,EAAE,YAAY,EAAE,QAAQ,EAAE,OAAO,CAAC,YAAY,CAAC,CAAC;IAE9D,IAAM,YAAY,GAAG,KAAK,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC;IAEjD,uCAAuC;IACvC,IAAI,MAAM,GAAyB,IAAI,CAAC;IACxC,IAAM,MAAM,GAAG,KAAK,CAAC,kBAAkB,CAAC,YAAY,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;IACpE,IAAI,iBAAG,EAAE,CAAC,SAAS,CAAC,eAAe,CAAC,KAAK,CAAC,EAAE;QAC1C,MAAM,GAAG,KAAK,CAAC,kBAAkB,CAAC,YAAY,EAAE,UAAU,EAAE,KAAK,CAAC,CAAC;KACpE;IAED,4BAA4B;IAC5B,IAAM,gBAAgB,GAA2C,EAAE,CAAC;IACpE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,aAAa,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;QACrD,IAAM,OAAO,GAAG,OAAO,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC;QACzC,IAAM,WAAW,GAAG,KAAK,CAAC;QAC1B,gBAAgB,CAAC,OAAO,CAAC;YACrB,KAAK,CAAC,kBAAkB,CAAC,YAAY,EAAE,OAAO,EAAE,WAAW,CAAC,CAAC;QACjE,gBAAgB,CAAC,WAAS,OAAS,CAAC;YAChC,KAAK,CAAC,kBAAkB,CAAC,YAAY,EAAE,WAAS,OAAS,EAAE,WAAW,CAAC,CAAC;KAC7E;IAED,OAAO;QACL,OAAO,SAAA;QACP,MAAM,QAAA;QACN,YAAY,cAAA;QACZ,gBAAgB,kBAAA;QAChB,YAAY,cAAA;QACZ,YAAY,cAAA;QACZ,MAAM,QAAA;QACN,MAAM,QAAA;KACP,CAAC;AACJ,CAAC;AA3DD,wCA2DC;AAED,SAAS,wBAAwB,CAC7B,UAAuB,EAAE,MAAoB;IAC/C,IAAI,UAAU,CAAC,MAAM,KAAK,MAAM,CAAC,MAAM,EAAE;QACvC,MAAM,KAAK,CACP,8BAA4B,UAAU,CAAC,MAAM,kBAAe;aAC5D,uBAAqB,MAAM,CAAC,MAAM,YAAS,CAAA,CAAC,CAAC;KAClD;IAED,UAAU,CAAC,OAAO,CAAC,UAAC,CAAC,EAAE,CAAC;QACtB,IAAM,MAAM,GAAG,CAAC,CAAC,YAAY,CAAC;QAC9B,IAAM,KAAK,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;QACxB,IAAM,MAAM,GAAG,KAAK,CAAC,KAAK,CAAC;QAE3B,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,MAAM,CAAC,EAAE;YACrC,MAAM,KAAK,CACP,iDAAiD;iBACjD,8BAA4B,MAAM,aAAQ,MAAM,gBAAa,CAAA,CAAC,CAAC;SACpE;QACD,oCAAoC;QACpC,IAAI,CAAC,CAAC,SAAS,IAAI,KAAK,CAAC,SAAS,EAAE;YAClC,OAAO;SACR;QAED,IAAM,SAAS,GAAG,CAAC,CAAC,QAAQ,CAAC;QAC7B,IAAM,SAAS,GAAG,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC;QAClE,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,SAAS,EAAE,SAAS,CAAC,EAAE;YAC3C,MAAM,KAAK,CACP,4DAA4D;iBAC5D,0BAAwB,SAAS,aAAQ,SAAS,gBAAa,CAAA,CAAC,CAAC;SACtE;IACH,CAAC,CAAC,CAAC;AACL,CAAC;AAED,SAAgB,UAAU,CACtB,KAAmB,EAAE,MAAmB,EAAE,MAAoB,EAC9D,MAAkB,EAClB,WACQ;IACV,wBAAwB,CAAC,MAAM,CAAC,YAAY,EAAE,MAAM,CAAC,CAAC;IACtD,wBAAwB,CAAC,CAAC,MAAM,CAAC,YAAY,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC;IAE1D,IAAM,MAAM,GAAG,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC;IACtC,IAAM,WAAW,GAAG,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC;IAC5C,IAAI,MAAM,CAAC,OAAO,CAAC,QAAQ,EAAE;QAC3B,KAAK,CAAC,4BAA4B,CAAC,MAAM,EAAE,WAAW,CAAC,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC;KAC5E;SAAM;QACL,KAAK,CAAC,sBAAsB,CAAC,MAAM,EAAE,WAAW,CAAC,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC;KACtE;IACD,KAAK,CAAC,UAAU,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC;IAEtC,uCAAuC;IACvC,IAAI,iBAAG,EAAE,CAAC,SAAS,CAAC,eAAe,CAAC,KAAK,CAAC,EAAE;QAC1C,IAAI,MAAM,CAAC,MAAM,KAAK,IAAI,EAAE;YAC1B,KAAK,CAAC,EAAE,CAAC,SAAS,CAAC,MAAM,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;SAC7C;KACF;IACD,IAAI,MAAM,CAAC,MAAM,KAAK,IAAI,EAAE;QAC1B,KAAK,CAAC,EAAE,CAAC,SAAS,CAAC,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;KACxC;IAED,0BAA0B;IAC1B,MAAM,CAAC,OAAO,CAAC,UAAC,KAAK,EAAE,CAAC;QACtB,IAAM,OAAO,GAAG,MAAM,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC;QAChD,IAAM,MAAM,GAAG,MAAM,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC;QAChD,IAAM,YAAY,GAAG,MAAM,CAAC,gBAAgB,CAAC,WAAS,OAAS,CAAC,CAAC;QAEjE,IAAI,MAAM,IAAI,IAAI,EAAE;YAClB,uEAAuE;YACvE,OAAO;SACR;QAED,IAAI,KAAK,CAAC,SAAS,EAAE;YACnB,8CAA8C;YAC9C,IAAI,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE;gBACvC,KAAK,CAAC,EAAE,CAAC,SAAS,CAAC,MAAM,EAAE,KAAK,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC;aACpD;iBAAM;gBACL,IAAI,IAAI,GAAG,KAAK,CAAC,aAAa,CAAC;gBAC/B,IAAI,CAAC,CAAC,IAAI,YAAY,YAAY,CAAC,EAAE;oBACnC,IAAI,GAAG,IAAI,YAAY,CAAC,IAAI,CAAC,CAAC;iBAC/B;gBACD,KAAK,CAAC,EAAE,CAAC,UAAU,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;aACnC;YACD,OAAO;SACR;QAED,yDAAyD;QACzD,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,IAAI,IAAI,IAAI,YAAY,IAAI,IAAI,EAAE;YACvD,KAAK,CAAC,EAAE,CAAC,SAAS,CAAC,YAAY,EAAE,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;SAClE;QAED,KAAK,CAAC,qBAAqB,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO,EAAE,MAAM,EAAE,CAAC,CAAC,CAAC;IAChE,CAAC,CAAC,CAAC;IAEH,IAAI,WAAW,IAAI,IAAI,EAAE;QACvB,WAAW,CAAC,KAAK,EAAE,MAAM,CAAC,YAAY,CAAC,CAAC;KACzC;IACD,KAAK,CAAC,cAAc,EAAE,CAAC;AACzB,CAAC;AAhED,gCAgEC;AAED,SAAgB,aAAa,CACzB,OAAqB,EAAE,MAAoB,EAAE,MAAkB;IACjE,IAAI,SAAS,GAAG,EAAE,CAAC;IACnB,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,UAAA,CAAC;QAC7B,IAAM,SAAS,GAAG,CAAC,CAAC,OAAO,IAAI,IAAI,IAAI,CAAC,CAAC,OAAO,CAAC,KAAK,IAAI,IAAI;YAC1D,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,UAAU,GAAG,CAAC,CAAC;QACnC,IAAM,QAAQ,GAAG,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAC;QAC9D,SAAS,IAAO,CAAC,CAAC,KAAK,SAAI,QAAQ,SAAI,SAAW,CAAC;IACrD,CAAC,CAAC,CAAC;IACH,IAAM,WAAW,GAAG,OAAO,CAAC,QAAQ,CAAC;IACrC,IAAI,GAAG,GAAG,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC;IACnC,sEAAsE;IACtE,GAAG,IAAI,GAAG,GAAG,SAAS,GAAG,GAAG,GAAG,WAAW,CAAC;IAC3C,OAAO,GAAG,CAAC;AACb,CAAC;AAdD,sCAcC"}