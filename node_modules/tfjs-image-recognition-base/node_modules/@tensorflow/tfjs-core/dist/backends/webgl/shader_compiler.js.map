{"version": 3, "file": "shader_compiler.js", "sourceRoot": "", "sources": ["../../../src/backends/webgl/shader_compiler.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;;GAeG;;AAEH,2DAA0D;AAC1D,iCAAmC;AACnC,+CAAwD;AACxD,oDAAsD;AAetD,SAAgB,UAAU,CACtB,UAAuB,EAAE,WAAsB,EAAE,QAAgB,EACjE,kBAA2B;IAC7B,IAAM,cAAc,GAAa,EAAE,CAAC;IACpC,UAAU,CAAC,OAAO,CAAC,UAAA,CAAC;QAClB,IAAM,IAAI,GAAG,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC;QAE1D,2DAA2D;QAC3D,IAAI,CAAC,CAAC,SAAS,CAAC,SAAS,EAAE;YACzB,cAAc,CAAC,IAAI,CACf,mBAAiB,CAAC,CAAC,IAAI,IAAG,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC,MAAI,IAAI,MAAG,CAAC,CAAC,CAAC,EAAE,OAAG,CAAC,CAAC;SAC/D;aAAM;YACL,cAAc,CAAC,IAAI,CAAC,uBAAqB,CAAC,CAAC,IAAI,MAAG,CAAC,CAAC;YACpD,cAAc,CAAC,IAAI,CAAC,uBAAqB,CAAC,CAAC,IAAI,MAAG,CAAC,CAAC;SACrD;IACH,CAAC,CAAC,CAAC;IACH,IAAM,kBAAkB,GAAG,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAErD,IAAM,oBAAoB,GACtB,UAAU;SACL,GAAG,CAAC,UAAA,CAAC,IAAI,OAAA,uBAAuB,CAAC,CAAC,EAAE,WAAW,EAAE,kBAAkB,CAAC,EAA3D,CAA2D,CAAC;SACrE,IAAI,CAAC,IAAI,CAAC,CAAC;IACpB,IAAM,WAAW,GAAG,WAAW,CAAC,QAAQ,CAAC;IACzC,IAAM,IAAI,GAAG,iCAAkB,EAAE,CAAC;IAClC,IAAM,yBAAyB,GAAG,4BAA4B,CAAC,IAAI,CAAC,CAAC;IACrE,IAAI,qBAA6B,CAAC;IAClC,IAAI,4BAAoC,CAAC;IACzC,IAAI,YAAY,GAAG,eAAe,CAAC,IAAI,CAAC,CAAC;IAEzC,IAAI,WAAW,CAAC,QAAQ,EAAE;QACxB,qBAAqB;YACjB,8BAA8B,CAAC,WAAW,CAAC,YAAY,EAAE,WAAW,CAAC,CAAC;QAC1E,4BAA4B,GAAG,6BAA6B,CAAC,IAAI,CAAC,CAAC;KACpE;SAAM;QACL,qBAAqB;YACjB,wBAAwB,CAAC,WAAW,CAAC,YAAY,EAAE,WAAW,CAAC,CAAC;QACpE,4BAA4B,GAAG,0BAA0B,CAAC,IAAI,CAAC,CAAC;KACjE;IAED,IAAI,kBAAkB,EAAE;QACtB,YAAY,IAAI,oBAAoB,CAAC;KACtC;IAED,IAAM,MAAM,GAAG;QACb,YAAY,EAAE,yBAAyB,EAAE,4BAA4B;QACrE,kBAAkB,EAAE,qBAAqB,EAAE,oBAAoB,EAAE,QAAQ;KAC1E,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IACb,OAAO,MAAM,CAAC;AAChB,CAAC;AAhDD,gCAgDC;AAED,SAAS,oBAAoB,CAAC,MAAiB;IAC7C,IAAM,KAAK,GAAG,MAAM,CAAC,SAAS,CAAC,YAAY,CAAC;IAC5C,QAAQ,KAAK,CAAC,MAAM,EAAE;QACpB,KAAK,CAAC;YACJ,OAAO,gBAAgB,CAAC,MAAM,CAAC,CAAC;QAClC,KAAK,CAAC;YACJ,OAAO,YAAY,CAAC,MAAM,CAAC,CAAC;QAC9B,KAAK,CAAC;YACJ,OAAO,YAAY,CAAC,MAAM,CAAC,CAAC;QAC9B,KAAK,CAAC;YACJ,OAAO,YAAY,CAAC,MAAM,CAAC,CAAC;QAC9B,KAAK,CAAC;YACJ,OAAO,YAAY,CAAC,MAAM,CAAC,CAAC;QAC9B,KAAK,CAAC;YACJ,OAAO,YAAY,CAAC,MAAM,CAAC,CAAC;QAC9B,KAAK,CAAC;YACJ,OAAO,YAAY,CAAC,MAAM,CAAC,CAAC;QAC9B;YACE,MAAM,IAAI,KAAK,CACR,KAAK,CAAC,MAAM,sBAAmB;gBAClC,uBAAuB,CAAC,CAAC;KAChC;AACH,CAAC;AAED,SAAS,0BAA0B,CAAC,MAAiB;IACnD,IAAM,KAAK,GAAG,MAAM,CAAC,SAAS,CAAC,YAAY,CAAC;IAC5C,QAAQ,KAAK,CAAC,MAAM,EAAE;QACpB,KAAK,CAAC;YACJ,OAAO,sBAAsB,CAAC,MAAM,CAAC,CAAC;QACxC,KAAK,CAAC;YACJ,OAAO,kBAAkB,CAAC,MAAM,CAAC,CAAC;QACpC,KAAK,CAAC;YACJ,OAAO,kBAAkB,CAAC,MAAM,CAAC,CAAC;QACpC,KAAK,CAAC;YACJ,OAAO,kBAAkB,CAAC,MAAM,CAAC,CAAC;QACpC;YACE,OAAO,kBAAkB,CAAC,MAAM,CAAC,CAAC;KACrC;AACH,CAAC;AAED,SAAS,uBAAuB,CAC5B,MAAiB,EAAE,YAAuB,EAC1C,kBAA0B;IAA1B,mCAAA,EAAA,0BAA0B;IAC5B,IAAI,GAAG,GAAG,EAAE,CAAC;IACb,IAAI,kBAAkB,EAAE;QACtB,GAAG,IAAI,0BAA0B,CAAC,MAAM,CAAC,CAAC;KAC3C;SAAM;QACL,GAAG,IAAI,oBAAoB,CAAC,MAAM,CAAC,CAAC;KACrC;IAED,IAAM,OAAO,GAAG,MAAM,CAAC,SAAS,CAAC,YAAY,CAAC;IAC9C,IAAM,QAAQ,GAAG,YAAY,CAAC,YAAY,CAAC;IAC3C,IAAI,OAAO,CAAC,MAAM,IAAI,QAAQ,CAAC,MAAM,EAAE;QACrC,IAAI,kBAAkB,EAAE;YACtB,GAAG,IAAI,8BAA8B,CAAC,MAAM,EAAE,YAAY,CAAC,CAAC;SAC7D;aAAM;YACL,GAAG,IAAI,wBAAwB,CAAC,MAAM,EAAE,YAAY,CAAC,CAAC;SACvD;KACF;IACD,OAAO,GAAG,CAAC;AACb,CAAC;AAED,SAAS,8BAA8B,CACnC,QAAkB,EAAE,WAA6B;IACnD,QAAQ,QAAQ,CAAC,MAAM,EAAE;QACvB,KAAK,CAAC;YACJ,OAAO,qBAAqB,EAAE,CAAC;QACjC,KAAK,CAAC;YACJ,OAAO,uBAAuB,CAAC,QAAoB,EAAE,WAAW,CAAC,CAAC;QACpE,KAAK,CAAC;YACJ,OAAO,uBAAuB,CAAC,QAA4B,EAAE,WAAW,CAAC,CAAC;QAC5E,KAAK,CAAC;YACJ,OAAO,uBAAuB,CAC1B,QAAoC,EAAE,WAAW,CAAC,CAAC;QACzD;YACE,OAAO,uBAAuB,CAAC,QAAQ,EAAE,WAAW,CAAC,CAAC;KACzD;AACH,CAAC;AAED,SAAS,wBAAwB,CAC7B,QAAkB,EAAE,WAA6B;IACnD,QAAQ,QAAQ,CAAC,MAAM,EAAE;QACvB,KAAK,CAAC;YACJ,OAAO,qBAAqB,EAAE,CAAC;QACjC,KAAK,CAAC;YACJ,OAAO,iBAAiB,CAAC,QAAoB,EAAE,WAAW,CAAC,CAAC;QAC9D,KAAK,CAAC;YACJ,OAAO,iBAAiB,CAAC,QAA4B,EAAE,WAAW,CAAC,CAAC;QACtE,KAAK,CAAC;YACJ,OAAO,iBAAiB,CACpB,QAAoC,EAAE,WAAW,CAAC,CAAC;QACzD,KAAK,CAAC;YACJ,OAAO,iBAAiB,CACpB,QAA4C,EAAE,WAAW,CAAC,CAAC;QACjE,KAAK,CAAC;YACJ,OAAO,iBAAiB,CACpB,QAAoD,EAAE,WAAW,CAAC,CAAC;QACzE,KAAK,CAAC;YACJ,OAAO,iBAAiB,CACpB,QAA4D,EAC5D,WAAW,CAAC,CAAC;QACnB;YACE,MAAM,IAAI,KAAK,CACR,QAAQ,CAAC,MAAM,4CAAyC,CAAC,CAAC;KACpE;AACH,CAAC;AAED,SAAS,4BAA4B,CAAC,IAAU;IAC9C,OAAO,kFAEM,IAAI,CAAC,SAAS,uCAE1B,CAAC;AACJ,CAAC;AAED,SAAS,0BAA0B,CAAC,IAAU;IAC5C,OAAO,8CAED,IAAI,CAAC,MAAM,sCAEhB,CAAC;AACJ,CAAC;AAED,SAAS,6BAA6B,CAAC,IAAU;IAC/C,OAAO,6CAED,IAAI,CAAC,MAAM,uBAEhB,CAAC;AACJ,CAAC;AAED,SAAS,eAAe,CAAC,IAAU;IACjC,IAAM,aAAa,GAAM,IAAI,CAAC,OAAO,oGAIjC,IAAI,CAAC,SAAS,6BACd,IAAI,CAAC,YAAY,iTAuBjB,IAAI,CAAC,gBAAgB,cACrB,IAAI,CAAC,gBAAgB,cACrB,IAAI,CAAC,WAAW,qlBAyBhB,iBAAiB,cACjB,iBAAiB,cACjB,iBAAiB,SACpB,CAAC;IAEF,OAAO,aAAa,CAAC;AACvB,CAAC;AAED,IAAM,iBAAiB,GAAG,ibAYzB,CAAC;AAEF,IAAM,iBAAiB,GAAG,oTAQzB,CAAC;AAEF,IAAM,iBAAiB,GAAG,0VASzB,CAAC;AAEF,IAAM,oBAAoB,GAAG,qWAW5B,CAAC;AAEF,SAAS,qBAAqB;IAC5B,OAAO,2DAIN,CAAC;AACJ,CAAC;AAED,SAAS,uBAAuB,CAC5B,KAAe,EAAE,QAA0B;IAC7C,IAAM,cAAc,GAChB,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IAC7D,IAAI,cAAc,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE;QAC3B,OAAO,0EAE2B,cAAc,CAAC,CAAC,CAAC,wBAElD,CAAC;KACH;IAED,IAAI,cAAc,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE;QAC3B,OAAO,0EAE2B,cAAc,CAAC,CAAC,CAAC,wBAElD,CAAC;KACH;IAED,OAAO,kHAG2B,cAAc,CAAC,CAAC,CAAC,UAAK,cAAc,CAAC,CAAC,CAAC,4CAC1C,cAAc,CAAC,CAAC,CAAC,+BAE/C,CAAC;AACJ,CAAC;AAED,SAAS,iBAAiB,CACtB,KAAe,EAAE,QAA0B;IAC7C,IAAI,QAAQ,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE;QACrB,OAAO,sEAEuB,QAAQ,CAAC,CAAC,CAAC,wBAExC,CAAC;KACH;IACD,IAAI,QAAQ,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE;QACrB,OAAO,sEAEuB,QAAQ,CAAC,CAAC,CAAC,wBAExC,CAAC;KACH;IACD,OAAO,kHAG2B,QAAQ,CAAC,CAAC,CAAC,UAAK,QAAQ,CAAC,CAAC,CAAC,uCACnC,QAAQ,CAAC,CAAC,CAAC,8BAEpC,CAAC;AACJ,CAAC;AAED,SAAS,uBAAuB,CAC5B,KAA+B,EAAE,QAA0B;IAC7D,IAAM,cAAc,GAChB,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IAC7D,IAAM,kBAAkB,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;IACnD,IAAM,aAAa,GAAG,kBAAkB,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;IAEnE,OAAO,oHAG2B,cAAc,CAAC,CAAC,CAAC,UAAK,cAAc,CAAC,CAAC,CAAC,4CAC1C,cAAc,CAAC,CAAC,CAAC,gDAE1B,aAAa,8BAChB,aAAa,wCAEL,kBAAkB,sCACnB,kBAAkB,sDAI3C,CAAC;AACJ,CAAC;AAED,SAAS,iBAAiB,CACtB,KAA+B,EAAE,QAA0B;IAC7D,IAAM,sBAAsB,GACxB,WAAW,CAAC,kCAAkC,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE,KAAK,CAAC,CAAC;IAE3E,OAAO,oHAG2B,QAAQ,CAAC,CAAC,CAAC,UAAK,QAAQ,CAAC,CAAC,CAAC,4CAC9B,QAAQ,CAAC,CAAC,CAAC,8BACpC,sBAAsB,8CAG3B,CAAC;AACJ,CAAC;AAED,SAAS,uBAAuB,CAC5B,KAAe,EAAE,QAA0B;IAC7C,IAAM,cAAc,GAChB,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IAE7D,IAAM,kBAAkB,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;IAClE,IAAM,aAAa,GACf,kBAAkB,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;IAChE,IAAI,cAAc,GAAG,aAAa,CAAC;IACnC,IAAI,OAAO,GAAG,EAAE,CAAC;IACjB,IAAI,MAAM,GAAG,SAAS,CAAC;IAEvB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;QACzC,cAAc,IAAI,KAAK,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;QAC9C,OAAO,GAAG,kBACD,CAAC,mBAAc,cAAc,2BACxB,CAAC,WAAM,cAAc,YAClC,GAAG,OAAO,CAAC;QACZ,MAAM,GAAG,MAAI,CAAC,OAAI,GAAG,MAAM,CAAC;KAC7B;IAED,OAAO,eACC,KAAK,CAAC,MAAM,4GAEc,cAAc,CAAC,CAAC,CAAC,UAAK,cAAc,CAAC,CAAC,CAAC,4CAC1C,cAAc,CAAC,CAAC,CAAC,gCAE1C,OAAO,kCAES,aAAa,8BAChB,aAAa,wCAEL,kBAAkB,sCACnB,kBAAkB,mCAE3B,KAAK,CAAC,MAAM,SAAI,MAAM,kBAEtC,CAAC;AACJ,CAAC;AAED,SAAS,iBAAiB,CACtB,KAAuC,EACvC,QAA0B;IAC5B,IAAM,sBAAsB,GAAG,WAAW,CAAC,kCAAkC,CACzE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,CAAC,EAAE,KAAK,CAAC,CAAC;IAElC,OAAO,+FAGM,QAAQ,CAAC,CAAC,CAAC,UAAK,QAAQ,CAAC,CAAC,CAAC,4CACT,QAAQ,CAAC,CAAC,CAAC,8BACpC,sBAAsB,kDAG3B,CAAC;AACJ,CAAC;AAED,SAAS,iBAAiB,CACtB,KAA+C,EAC/C,QAA0B;IAC5B,IAAM,sBAAsB,GAAG,WAAW,CAAC,kCAAkC,CACzE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,CAAC,EAAE,KAAK,CAAC,CAAC;IAExC,OAAO,sFAEyC,QAAQ,CAAC,CAAC,CAAC,wCAC9B,QAAQ,CAAC,CAAC,CAAC,8CAET,QAAQ,CAAC,CAAC,CAAC,gCAEpC,sBAAsB,0FAK3B,CAAC;AACJ,CAAC;AAED,SAAS,iBAAiB,CACtB,KAAuD,EACvD,QAA0B;IAC5B,IAAM,sBAAsB,GAAG,WAAW,CAAC,kCAAkC,CACzE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,EAAE,KAAK,CAAC,CAAC;IAE9C,OAAO,+FAGM,QAAQ,CAAC,CAAC,CAAC,UAAK,QAAQ,CAAC,CAAC,CAAC,4CACT,QAAQ,CAAC,CAAC,CAAC,gCAEpC,sBAAsB,0FAK3B,CAAC;AACJ,CAAC;AAED,SAAS,uBAAuB,CAC5B,KAAuB,EAAE,QAA0B;IACrD,IAAM,cAAc,GAChB,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IAC7D,IAAI,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,QAAQ,CAAC,EAAE;QACrC,OAAO,oFAEmC,cAAc,CAAC,CAAC,CAAC,UACvD,cAAc,CAAC,CAAC,CAAC,uBAEpB,CAAC;KACH;IAED,6CAA6C;IAC7C,IAAM,kBAAkB,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;IAEnD;;;;;;;;OAQG;IACH,OAAO,oHAG2B,cAAc,CAAC,CAAC,CAAC,UAAK,cAAc,CAAC,CAAC,CAAC,8CAE1C,cAAc,CAAC,CAAC,CAAC,mDACrB,kBAAkB,sCACnB,kBAAkB,mDAI3C,CAAC;AACJ,CAAC;AAED,SAAS,iBAAiB,CACtB,KAAuB,EAAE,QAA0B;IACrD,IAAI,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,QAAQ,CAAC,EAAE;QACrC,OAAO,gFAE+B,QAAQ,CAAC,CAAC,CAAC,UAAK,QAAQ,CAAC,CAAC,CAAC,uBAEhE,CAAC;KACH;IACD,IAAI,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE;QAClB,OAAO,0HAG2B,QAAQ,CAAC,CAAC,CAAC,UAAK,QAAQ,CAAC,CAAC,CAAC,8CAC9B,QAAQ,CAAC,CAAC,CAAC,mEAGzC,CAAC;KACH;IACD,IAAI,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE;QAClB,OAAO,0HAG2B,QAAQ,CAAC,CAAC,CAAC,UAAK,QAAQ,CAAC,CAAC,CAAC,8CAC9B,QAAQ,CAAC,CAAC,CAAC,mEAGzC,CAAC;KACH;IACD,OAAO,oHAG2B,QAAQ,CAAC,CAAC,CAAC,UAAK,QAAQ,CAAC,CAAC,CAAC,4CAC9B,QAAQ,CAAC,CAAC,CAAC,8CACpB,KAAK,CAAC,CAAC,CAAC,qCACJ,KAAK,CAAC,CAAC,CAAC,4CAGjC,CAAC;AACJ,CAAC;AAED,SAAS,wBAAwB,CAAC,OAAe;IAC/C,OAAO,WAAS,OAAS,CAAC;AAC5B,CAAC;AAED,SAAS,sBAAsB,CAAC,SAAoB;IAClD,IAAM,OAAO,GAAG,SAAS,CAAC,IAAI,CAAC;IAC/B,IAAM,QAAQ,GAAG,KAAK,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,GAAG,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;IAC5E,IAAM,IAAI,GAAG,iCAAkB,EAAE,CAAC;IAClC,OAAO,gBACE,QAAQ,2BACJ,IAAI,CAAC,SAAS,SAAI,OAAO,0BAErC,CAAC;AACJ,CAAC;AAED,SAAS,gBAAgB,CAAC,SAAoB;IAC5C,IAAM,OAAO,GAAG,SAAS,CAAC,IAAI,CAAC;IAC/B,IAAM,QAAQ,GAAG,KAAK,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,GAAG,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;IAC5E,IAAI,SAAS,CAAC,SAAS,CAAC,SAAS,EAAE;QACjC,OAAO,WAAS,QAAQ,mBAAc,OAAO,OAAI,CAAC;KACnD;IACK,IAAA,iCAAiD,EAAhD,eAAO,EAAE,eAAuC,CAAC;IACxD,IAAI,OAAO,KAAK,CAAC,IAAI,OAAO,KAAK,CAAC,EAAE;QAClC,OAAO,mBACG,QAAQ,2CACS,OAAO,8BAEjC,CAAC;KACH;IAEK,IAAA,iCAA6C,EAA5C,aAAK,EAAE,aAAqC,CAAC;IACpD,IAAM,MAAM,GAAG,wBAAwB,CAAC,OAAO,CAAC,CAAC;IACjD,OAAO,iBACG,QAAQ,yCACS,KAAK,UAAK,KAAK,UAAK,MAAM,uCAC1B,OAAO,sBAEjC,CAAC;AACJ,CAAC;AAED,SAAS,kBAAkB,CAAC,SAAoB;IAC9C,IAAM,OAAO,GAAG,SAAS,CAAC,IAAI,CAAC;IAC/B,IAAM,QAAQ,GAAG,KAAK,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,GAAG,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;IAC5E,IAAM,QAAQ,GAAG,SAAS,CAAC,SAAS,CAAC,QAAQ,CAAC;IAC9C,IAAM,cAAc,GAChB,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IAC7D,IAAM,IAAI,GAAG,iCAAkB,EAAE,CAAC;IAElC,OAAO,gBACE,QAAQ,gEAET,cAAc,CAAC,CAAC,CAAC,UAAK,cAAc,CAAC,CAAC,CAAC,gCAClC,IAAI,CAAC,SAAS,SAAI,OAAO,sBAErC,CAAC;AACJ,CAAC;AAED,SAAS,YAAY,CAAC,SAAoB;IACxC,IAAM,OAAO,GAAG,SAAS,CAAC,IAAI,CAAC;IAC/B,IAAM,QAAQ,GAAG,KAAK,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,GAAG,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;IAE5E,IAAI,SAAS,CAAC,SAAS,CAAC,SAAS,EAAE;QACjC,wEAAwE;QACxE,OAAO,mBACG,QAAQ,+BACZ,iBAAiB,CAAC,SAAS,CAAC,oBAEjC,CAAC;KACH;IAED,IAAM,QAAQ,GAAG,SAAS,CAAC,SAAS,CAAC,QAAQ,CAAC;IAC9C,IAAM,KAAK,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC1B,IAAM,KAAK,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;IAE1B,IAAI,KAAK,KAAK,CAAC,IAAI,KAAK,KAAK,CAAC,EAAE;QAC9B,OAAO,mBACG,QAAQ,oDACS,OAAO,8BAEjC,CAAC;KACH;IACD,IAAM,MAAM,GAAG,wBAAwB,CAAC,OAAO,CAAC,CAAC;IACjD,IAAI,KAAK,KAAK,CAAC,EAAE;QACf,OAAO,mBACG,QAAQ,kEACuB,MAAM,mBAAc,KAAK,2CACvC,OAAO,0BAEjC,CAAC;KACH;IACD,IAAI,KAAK,KAAK,CAAC,EAAE;QACf,OAAO,mBACG,QAAQ,6DACkB,MAAM,mBAAc,KAAK,gDAClC,OAAO,0BAEjC,CAAC;KACH;IACD,OAAO,iBACG,QAAQ,kDACS,KAAK,UAAK,KAAK,kBAAa,MAAM,uCAClC,OAAO,sBAEjC,CAAC;AACJ,CAAC;AAED,SAAS,kBAAkB,CAAC,SAAoB;IAC9C,IAAM,KAAK,GAAG,SAAS,CAAC,SAAS,CAAC,YAAY,CAAC;IAC/C,IAAM,OAAO,GAAG,SAAS,CAAC,IAAI,CAAC;IAC/B,IAAM,QAAQ,GAAG,KAAK,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,GAAG,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;IAC5E,IAAM,QAAQ,GAAG,SAAS,CAAC,SAAS,CAAC,QAAQ,CAAC;IAE9C,IAAM,OAAO,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC5B,IAAM,OAAO,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC5B,IAAM,IAAI,GAAG,iCAAkB,EAAE,CAAC;IAClC,IAAI,QAAQ,IAAI,IAAI,IAAI,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,QAAQ,CAAC,EAAE;QACzD,OAAO,kBACE,QAAQ,iFACgC,OAAO,YAAO,OAAO,+BAEzD,IAAI,CAAC,SAAS,SAAI,OAAO,0BAErC,CAAC;KACH;IAED,IAAM,cAAc,GAChB,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IAC7D,IAAM,YAAY,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;IAE7C,OAAO,gBACE,QAAQ,6DACc,YAAY,UAAK,cAAc,CAAC,CAAC,CAAC,UAC7D,cAAc,CAAC,CAAC,CAAC,mCACR,IAAI,CAAC,SAAS,SAAI,OAAO,sBAErC,CAAC;AACJ,CAAC;AAED,SAAS,YAAY,CAAC,SAAoB;IACxC,IAAM,KAAK,GAAG,SAAS,CAAC,SAAS,CAAC,YAAY,CAAC;IAC/C,IAAM,OAAO,GAAG,SAAS,CAAC,IAAI,CAAC;IAC/B,IAAM,QAAQ,GAAG,KAAK,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,GAAG,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;IAC5E,IAAM,QAAQ,GAAG,SAAS,CAAC,SAAS,CAAC,QAAQ,CAAC;IAE9C,IAAI,QAAQ,IAAI,IAAI,IAAI,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,QAAQ,CAAC,EAAE;QACzD,IAAM,SAAO,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;QAC5B,IAAM,SAAO,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;QAC5B,OAAO,iBACC,QAAQ,+EAC+B,SAAO,YAAO,SAAO,yCAC3C,OAAO,sBAEjC,CAAC;KACD;IAEK,IAAA,6BAA+C,EAA9C,sBAAQ,EAAE,sBAAoC,CAAC;IACtD,IAAM,aAAa,GAAG,QAAQ,CAAC;IAC/B,IAAI,aAAa,CAAC,MAAM,GAAG,KAAK,CAAC,MAAM,EAAE;QACvC,IAAM,YAAY,GAAG,gBAAgB,CAAC,SAAS,EAAE,aAAa,CAAC,CAAC;QAChE,IAAM,MAAM,GAAG,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;QAC9B,OAAO,aACH,oBAAoB,CAAC,YAAY,CAAC,sBAC5B,QAAQ,6CACL,QAAQ,SAAI,iBAAiB,CAAC,MAAM,EAAE,QAAQ,CAAC,sBAE3D,CAAC;KACH;IAED,IAAI,SAAS,CAAC,SAAS,CAAC,SAAS,EAAE;QACjC,wEAAwE;QACxE,OAAO,mBACG,QAAQ,iFAC+B,KAAK,CAAC,CAAC,CAAC,yBACnD,iBAAiB,CAAC,SAAS,CAAC,oBAEjC,CAAC;KACH;IAED,IAAM,OAAO,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC5B,IAAM,OAAO,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC5B,IAAM,MAAM,GAAG,wBAAwB,CAAC,OAAO,CAAC,CAAC;IACjD,IAAI,OAAO,KAAK,CAAC,EAAE;QACjB,oEAAoE;QACpE,OAAO,iBACC,QAAQ,qEACqB,MAAM,gBAAW,KAAK,CAAC,CAAC,CAAC,6DACtB,OAAO,yCACtB,OAAO,sBAEjC,CAAC;KACD;IACD,IAAI,OAAO,KAAK,CAAC,EAAE;QACjB,oEAAoE;QACpE,OAAO,iBACC,QAAQ,qEACqB,MAAM,gBAAW,KAAK,CAAC,CAAC,CAAC,wDAC3B,OAAO,8CACjB,OAAO,sBAEjC,CAAC;KACD;IAED,OAAO,eACC,QAAQ,6HAEM,KAAK,CAAC,CAAC,CAAC,iBAAY,MAAM,oCACvB,OAAO,UAAK,OAAO,4CACnB,OAAO,kBAEjC,CAAC;AACF,CAAC;AAED,SAAS,kBAAkB,CAAC,SAAoB;IAC9C,IAAM,KAAK,GAAG,SAAS,CAAC,SAAS,CAAC,YAAY,CAAC;IAC/C,IAAM,OAAO,GAAG,SAAS,CAAC,IAAI,CAAC;IAC/B,IAAM,QAAQ,GAAG,KAAK,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,GAAG,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;IAC5E,IAAM,QAAQ,GAAG,SAAS,CAAC,SAAS,CAAC,QAAQ,CAAC;IAC9C,IAAM,cAAc,GAChB,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IAE7D,IAAI,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE;QAClB,IAAM,aAAa,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;QACrC,IAAM,QAAQ,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACxB,IAAM,YAAY,GAAG,gBAAgB,CAAC,SAAS,EAAE,aAAa,CAAC,CAAC;QAChE,IAAM,MAAM,GAAG,CAAC,GAAG,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;QACnC,OAAO,eACD,0BAA0B,CAAC,YAAY,CAAC,uBACnC,QAAQ,sDACJ,QAAQ,SAAI,iBAAiB,CAAC,MAAM,EAAE,QAAQ,CAAC,0BAE3D,CAAC;KACL;IAED,IAAM,OAAO,GAAG,cAAc,CAAC,CAAC,CAAC,CAAC;IAClC,IAAM,OAAO,GAAG,cAAc,CAAC,CAAC,CAAC,CAAC;IAElC,IAAM,YAAY,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;IAC7C,IAAM,aAAa,GAAG,YAAY,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;IAC7D,IAAM,IAAI,GAAG,iCAAkB,EAAE,CAAC;IAElC,OAAO,gBACE,QAAQ,8EAET,OAAO,UAAK,OAAO,UAAK,aAAa,UAAK,YAAY,sCACjD,IAAI,CAAC,SAAS,SAAI,OAAO,sBAErC,CAAC;AACJ,CAAC;AAED,SAAS,YAAY,CAAC,SAAoB;IACxC,IAAM,KAAK,GAAG,SAAS,CAAC,SAAS,CAAC,YAAY,CAAC;IAC/C,IAAM,OAAO,GAAG,SAAS,CAAC,IAAI,CAAC;IAC/B,IAAM,QAAQ,GAAG,KAAK,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,GAAG,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;IAC5E,IAAM,OAAO,GAAG,KAAK,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;IACpC,IAAM,OAAO,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;IAEnB,IAAA,6BAA+C,EAA9C,sBAAQ,EAAE,sBAAoC,CAAC;IACtD,IAAM,aAAa,GAAG,QAAQ,CAAC;IAC/B,IAAI,aAAa,CAAC,MAAM,GAAG,KAAK,CAAC,MAAM,EAAE;QACvC,IAAM,YAAY,GAAG,gBAAgB,CAAC,SAAS,EAAE,aAAa,CAAC,CAAC;QAChE,IAAM,MAAM,GAAG,CAAC,KAAK,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;QACvC,OAAO,eACD,oBAAoB,CAAC,YAAY,CAAC,wBAC5B,QAAQ,0DACL,QAAQ,SAAI,iBAAiB,CAAC,MAAM,EAAE,QAAQ,CAAC,0BAE3D,CAAC;KACL;IAED,IAAI,SAAS,CAAC,SAAS,CAAC,SAAS,EAAE;QACjC,wEAAwE;QACxE,OAAO,mBACG,QAAQ,8HAEW,OAAO,UAAK,OAAO,yBAC1C,iBAAiB,CAAC,SAAS,CAAC,oBAEjC,CAAC;KACH;IAED,IAAM,QAAQ,GAAG,SAAS,CAAC,SAAS,CAAC,QAAQ,CAAC;IAC9C,IAAM,OAAO,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC5B,IAAM,OAAO,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC5B,IAAM,UAAU,GAAG,SAAS,CAAC,SAAS,CAAC,UAAU,CAAC;IAClD,IAAI,OAAO,KAAK,OAAO,IAAI,UAAU,IAAI,IAAI,EAAE;QAC7C,mEAAmE;QACnE,OAAO,qBACK,QAAQ,+HAE4B,OAAO,6FAE/B,OAAO,YAAO,OAAO,6CAChB,OAAO,8BAEjC,CAAC;KACL;IAED,IAAI,OAAO,KAAK,OAAO,IAAI,UAAU,IAAI,IAAI,EAAE;QAC7C,mEAAmE;QACnE,OAAO,iBACC,QAAQ,qFAC0B,KAAK,CAAC,CAAC,CAAC,qGAED,OAAO,YAAO,OAAO,yCAC7C,OAAO,sBAEjC,CAAC;KACD;IAED,IAAM,MAAM,GAAG,wBAAwB,CAAC,OAAO,CAAC,CAAC;IACjD,OAAO,mBACK,QAAQ,gJAEM,OAAO,iBAAY,OAAO,mBAAc,MAAM,wCAC3C,OAAO,UAAK,OAAO,gDACnB,OAAO,wBAEnC,CAAC;AACJ,CAAC;AAED,SAAS,kBAAkB,CAAC,SAAoB;IAC9C,IAAM,KAAK,GAAG,SAAS,CAAC,SAAS,CAAC,YAAY,CAAC;IAC/C,IAAM,IAAI,GAAG,KAAK,CAAC,MAAM,CAAC;IAC1B,IAAM,OAAO,GAAG,SAAS,CAAC,IAAI,CAAC;IAC/B,IAAM,QAAQ,GAAG,KAAK,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,GAAG,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;IAC5E,IAAM,QAAQ,GAAG,SAAS,CAAC,SAAS,CAAC,QAAQ,CAAC;IAC9C,IAAM,cAAc,GAChB,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IAC7D,IAAM,OAAO,GAAG,cAAc,CAAC,CAAC,CAAC,CAAC;IAClC,IAAM,OAAO,GAAG,cAAc,CAAC,CAAC,CAAC,CAAC;IAElC,IAAM,YAAY,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;IACpD,IAAI,aAAa,GAAG,YAAY,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;IAClE,IAAI,MAAM,GAAG,yBAAyB,CAAC;IACvC,IAAI,KAAK,GAAG,SAAO,aAAa,uBAAkB,YAAY,iBAAc,CAAC;IAC7E,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;QACjC,MAAM,GAAG,UAAQ,CAAC,OAAI,GAAG,MAAM,CAAC;QAChC,aAAa,IAAI,KAAK,CAAC,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;QACrC,KAAK,GAAG,MAAI,CAAC,WAAM,aAAa,QAAK,GAAG,KAAK,CAAC;KAC/C;IACD,IAAM,IAAI,GAAG,iCAAkB,EAAE,CAAC;IAClC,OAAO,gBACE,QAAQ,SAAI,MAAM,+BACT,KAAK,oCACE,OAAO,2CACA,OAAO,8DACY,OAAO,UAAK,OAAO,yBACzD,IAAI,CAAC,SAAS,SAAI,OAAO,sBAErC,CAAC;AACJ,CAAC;AAED,SAAS,YAAY,CAAC,SAAoB;IACxC,IAAM,KAAK,GAAG,SAAS,CAAC,SAAS,CAAC,YAAY,CAAC;IAC/C,IAAM,OAAO,GAAG,SAAS,CAAC,IAAI,CAAC;IAC/B,IAAM,QAAQ,GAAG,KAAK,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,GAAG,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;IAC5E,IAAM,OAAO,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;IACzB,IAAM,OAAO,GAAG,KAAK,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC;IACnC,IAAM,OAAO,GAAG,KAAK,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC;IAE7B,IAAA,6BAA+C,EAA9C,sBAAQ,EAAE,sBAAoC,CAAC;IACtD,IAAI,QAAQ,CAAC,MAAM,GAAG,KAAK,CAAC,MAAM,EAAE;QAClC,IAAM,YAAY,GAAG,gBAAgB,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC;QAC3D,IAAM,MAAM,GAAG,CAAC,KAAK,EAAE,KAAK,EAAE,OAAO,EAAE,QAAQ,CAAC,CAAC;QACjD,OAAO,aACH,oBAAoB,CAAC,YAAY,CAAC,sBAC5B,QAAQ,oEACL,QAAQ,SAAI,iBAAiB,CAAC,MAAM,EAAE,QAAQ,CAAC,sBAE3D,CAAC;KACH;IAED,IAAI,SAAS,CAAC,SAAS,CAAC,SAAS,EAAE;QACjC,wEAAwE;QACxE,OAAO,mBACG,QAAQ,kJAEW,OAAO,UAAK,OAAO,UAAK,OAAO,yBACtD,iBAAiB,CAAC,SAAS,CAAC,oBAEjC,CAAC;KACH;IAED,IAAM,UAAU,GAAG,SAAS,CAAC,SAAS,CAAC,UAAU,CAAC;IAClD,IAAM,QAAQ,GAAG,SAAS,CAAC,SAAS,CAAC,QAAQ,CAAC;IAC9C,IAAM,OAAO,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC5B,IAAM,OAAO,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;IAE5B,IAAI,OAAO,KAAK,OAAO,IAAI,UAAU,IAAI,IAAI,EAAE;QAC7C,mEAAmE;QACnE,OAAO,mBACG,QAAQ,6KAIC,OAAO,UAAK,OAAO,yFAEhB,OAAO,YAAO,OAAO,2CAChB,OAAO,0BAEjC,CAAC;KACH;IACD,IAAI,OAAO,KAAK,OAAO,IAAI,UAAU,IAAI,IAAI,EAAE;QAC7C,mEAAmE;QACnE,OAAO,mBACG,QAAQ,oIAEU,KAAK,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,UAAK,KAAK,CAAC,CAAC,CAAC,6HAGvC,OAAO,YAAO,OAAO,2CACf,OAAO,0BAEjC,CAAC;KACH;IAED,IAAM,MAAM,GAAG,wBAAwB,CAAC,OAAO,CAAC,CAAC;IACjD,OAAO,iBACG,QAAQ,wJAEM,OAAO,iBAAY,OAAO,8BAChC,OAAO,+CACE,OAAO,UAAK,OAAO,kBAAa,MAAM,uCACtC,OAAO,sBAEjC,CAAC;AACJ,CAAC;AAED,SAAS,YAAY,CAAC,SAAoB;IACxC,IAAM,KAAK,GAAG,SAAS,CAAC,SAAS,CAAC,YAAY,CAAC;IAC/C,IAAM,OAAO,GAAG,SAAS,CAAC,IAAI,CAAC;IAC/B,IAAM,QAAQ,GAAG,KAAK,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,GAAG,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;IAC5E,IAAM,OAAO,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;IACzB,IAAM,OAAO,GAAG,KAAK,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC;IACnC,IAAM,OAAO,GAAG,KAAK,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC;IACnC,IAAM,OAAO,GAAG,KAAK,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC;IAE7B,IAAA,6BAA+C,EAA9C,sBAAQ,EAAE,sBAAoC,CAAC;IACtD,IAAI,QAAQ,CAAC,MAAM,GAAG,KAAK,CAAC,MAAM,EAAE;QAClC,IAAM,YAAY,GAAG,gBAAgB,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC;QAC3D,IAAM,MAAM,GAAG,CAAC,KAAK,EAAE,KAAK,EAAE,OAAO,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAAC;QAC3D,OAAO,aACH,oBAAoB,CAAC,YAAY,CAAC,sBAC5B,QAAQ,gFACL,QAAQ,SAAI,iBAAiB,CAAC,MAAM,EAAE,QAAQ,CAAC,sBAE3D,CAAC;KACH;IAED,IAAI,SAAS,CAAC,SAAS,CAAC,SAAS,EAAE;QACjC,wEAAwE;QACxE,OAAO,mBACG,QAAQ,sJAGL,OAAO,UAAK,OAAO,UAAK,OAAO,UAAK,OAAO,yCAElD,iBAAiB,CAAC,SAAS,CAAC,oBAEjC,CAAC;KACH;IAED,IAAM,UAAU,GAAG,SAAS,CAAC,SAAS,CAAC,UAAU,CAAC;IAClD,IAAM,QAAQ,GAAG,SAAS,CAAC,SAAS,CAAC,QAAQ,CAAC;IAC9C,IAAM,OAAO,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC5B,IAAM,OAAO,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;IAE5B,IAAI,OAAO,KAAK,OAAO,IAAI,UAAU,IAAI,IAAI,EAAE;QAC7C,mEAAmE;QACnE,OAAO,mBACG,QAAQ,oLAGU,OAAO,UAAK,OAAO,UAAK,OAAO,yFAErC,OAAO,YAAO,OAAO,2CAChB,OAAO,0BAEjC,CAAC;KACH;IAED,IAAI,OAAO,KAAK,OAAO,IAAI,UAAU,IAAI,IAAI,EAAE;QAC7C,mEAAmE;QACnE,OAAO,mBACG,QAAQ,qJAGL,KAAK,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,0BAC9B,KAAK,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,UAAK,KAAK,CAAC,CAAC,CAAC,oHAGxB,OAAO,YAAO,OAAO,2CACf,OAAO,0BAEjC,CAAC;KACH;IAED,IAAM,MAAM,GAAG,wBAAwB,CAAC,OAAO,CAAC,CAAC;IACjD,OAAO,iBACG,QAAQ,oKAEM,OAAO,iBAAY,OAAO,mBAAc,OAAO,+BACpD,OAAO,oBAAe,MAAM,sCACpB,OAAO,UAAK,OAAO,8CACnB,OAAO,sBAEjC,CAAC;AACJ,CAAC;AAED,SAAS,YAAY,CAAC,SAAoB;IACxC,IAAM,KAAK,GAAG,SAAS,CAAC,SAAS,CAAC,YAAY,CAAC;IAC/C,IAAM,OAAO,GAAG,SAAS,CAAC,IAAI,CAAC;IAC/B,IAAM,QAAQ,GAAG,KAAK,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,GAAG,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;IAEtE,IAAA,6BAA+C,EAA9C,sBAAQ,EAAE,sBAAoC,CAAC;IACtD,IAAI,QAAQ,CAAC,MAAM,GAAG,KAAK,CAAC,MAAM,EAAE;QAClC,IAAM,YAAY,GAAG,gBAAgB,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC;QAC3D,IAAM,MAAM,GAAG,CAAC,KAAK,EAAE,KAAK,EAAE,OAAO,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAAC;QACrE,OAAO,aACH,oBAAoB,CAAC,YAAY,CAAC,sBAC5B,QAAQ,iHAEL,QAAQ,SAAI,iBAAiB,CAAC,MAAM,EAAE,QAAQ,CAAC,sBAE3D,CAAC;KACH;IAED,IAAM,OAAO,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;IACzB,IAAM,OAAO,GAAG,KAAK,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC;IACnC,IAAM,OAAO,GAAG,KAAK,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC;IACnC,IAAM,OAAO,GAAG,KAAK,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC;IACnC,IAAM,OAAO,GAAG,KAAK,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC;IAEnC,IAAI,SAAS,CAAC,SAAS,CAAC,SAAS,EAAE;QACjC,wEAAwE;QACxE,OAAO,mBACG,QAAQ,yLAIL,OAAO,UAAK,OAAO,UAAK,OAAO,UAAK,OAAO,kFAGzC,OAAO,yBAChB,iBAAiB,CAAC,SAAS,CAAC,oBAEjC,CAAC;KACH;IAED,IAAM,UAAU,GAAG,SAAS,CAAC,SAAS,CAAC,UAAU,CAAC;IAClD,IAAM,QAAQ,GAAG,SAAS,CAAC,SAAS,CAAC,QAAQ,CAAC;IAC9C,IAAM,OAAO,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC5B,IAAM,OAAO,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC5B,IAAI,OAAO,KAAK,OAAO,IAAI,UAAU,IAAI,IAAI,EAAE;QAC7C,mEAAmE;QACnE,OAAO,mBACG,QAAQ,sMAIL,OAAO,UAAK,OAAO,UAAK,OAAO,UAAK,OAAO,sHAGlC,OAAO,YAAO,OAAO,2CAChB,OAAO,0BAEjC,CAAC;KACH;IACD,IAAI,OAAO,KAAK,OAAO,IAAI,UAAU,IAAI,IAAI,EAAE;QAC7C,mEAAmE;QACnE,OAAO,mBACG,QAAQ,0KAGL,KAAK,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,0BACzC,KAAK,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,0BAC9B,KAAK,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,0BACnB,KAAK,CAAC,CAAC,CAAC,iIAGA,OAAO,YAAO,OAAO,2CACf,OAAO,0BAEjC,CAAC;KACH;IACD,IAAM,MAAM,GAAG,wBAAwB,CAAC,OAAO,CAAC,CAAC;IACjD,OAAO,iBACG,QAAQ,mMAGM,OAAO,iBAAY,OAAO,mBAAc,OAAO,+BACpD,OAAO,oBAAe,OAAO,oBAAe,MAAM,sCAC1C,OAAO,UAAK,OAAO,8CACnB,OAAO,sBAEjC,CAAC;AACJ,CAAC;AAED,SAAS,iBAAiB,CAAC,SAAoB;IAC7C,IAAM,OAAO,GAAG,SAAS,CAAC,IAAI,CAAC;IAC/B,IAAM,MAAM,GAAG,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC;IAEpE,IAAI,MAAM,GAAG,CAAC,EAAE;QACd,OAAO,YAAU,OAAO,MAAG,CAAC;KAC7B;IACD,OAAO,+BACiB,MAAM,0DAEf,OAAO,6BAGrB,CAAC;AACJ,CAAC;AAED,SAAS,8BAA8B,CACnC,SAAoB,EAAE,YAAuB;IAC/C,IAAM,OAAO,GAAG,SAAS,CAAC,IAAI,CAAC;IAC/B,IAAM,cAAc,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,GAAG,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;IAC1E,IAAM,QAAQ,GAAG,KAAK,GAAG,cAAc,GAAG,aAAa,CAAC;IACxD,IAAM,MAAM,GAAG,SAAS,CAAC,SAAS,CAAC,YAAY,CAAC,MAAM,CAAC;IACvD,IAAM,OAAO,GAAG,YAAY,CAAC,YAAY,CAAC,MAAM,CAAC;IAEjD,IAAM,aAAa,GAAG,iCAAgB,CAClC,SAAS,CAAC,SAAS,CAAC,YAAY,EAAE,YAAY,CAAC,YAAY,CAAC,CAAC;IAEjE,IAAM,IAAI,GAAG,iBAAiB,CAAC,OAAO,CAAC,CAAC;IACxC,IAAM,QAAQ,GAAG,OAAO,GAAG,MAAM,CAAC;IAClC,IAAI,aAAqB,CAAC;IAC1B,IAAM,MAAM,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;IAE9C,IAAI,MAAM,KAAK,CAAC,EAAE;QAChB,aAAa,GAAG,EAAE,CAAC;KACpB;SAAM,IAAI,OAAO,GAAG,CAAC,IAAI,aAAa,CAAC,MAAM,IAAI,CAAC,EAAE;QACnD,aAAa,GAAG,aAAa,CAAC;KAC/B;SAAM;QACL,aAAa;YACT,aAAa,CAAC,GAAG,CAAC,UAAA,CAAC,IAAI,OAAA,YAAU,MAAM,CAAC,CAAC,GAAG,QAAQ,CAAC,UAAO,EAArC,CAAqC,CAAC;iBACxD,IAAI,CAAC,IAAI,CAAC,CAAC;KACrB;IACD,IAAI,qBAAqB,GAAG,EAAE,CAAC;IAC/B,IAAI,OAAO,GAAG,CAAC,IAAI,MAAM,GAAG,CAAC,EAAE;QAC7B,qBAAqB,GAAG,QAAQ,CAAC;KAClC;SAAM;QACL,qBAAqB,GAAG,SAAS,CAAC,SAAS,CAAC,YAAY;aAC3B,GAAG,CAAC,UAAC,CAAC,EAAE,CAAC,IAAK,OAAA,YAAU,MAAM,CAAC,CAAC,GAAG,QAAQ,CAAG,EAAhC,CAAgC,CAAC;aAC/C,IAAI,CAAC,IAAI,CAAC,CAAC;KACzC;IAED,IAAI,MAAM,GAAG,qBAAqB,CAAC;IACnC,IAAM,MAAM,GAAG,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC;IACpE,IAAM,aAAa,GAAG,MAAM,KAAK,CAAC,CAAC;IACnC,IAAM,OAAO,GAAG,IAAI,CAAC,aAAa,CAAC,YAAY,CAAC,YAAY,CAAC,CAAC;IAC9D,IAAM,cAAc,GAAG,OAAO,KAAK,CAAC,CAAC;IAErC,IAAI,MAAM,KAAK,CAAC,IAAI,CAAC,aAAa,IAAI,CAAC,cAAc,EAAE;QACrD,MAAM,GAAG,4DAER,CAAC;KACH;SAAM,IAAI,aAAa,IAAI,CAAC,cAAc,EAAE;QAC3C,IAAI,OAAO,KAAK,CAAC,EAAE;YACjB,MAAM,GAAG,sEAER,CAAC;SACH;aAAM;YACL,MAAM,GAAG,+CAER,CAAC;SACH;KACF;SAAM,IAAI,aAAa,CAAC,MAAM,EAAE;QAC/B,IAAM,IAAI,GAAG,MAAM,GAAG,CAAC,CAAC;QACxB,IAAM,IAAI,GAAG,MAAM,GAAG,CAAC,CAAC;QAExB,IAAI,aAAa,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,aAAa,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE;YACxE,MAAM,GAAG,6BAA6B,CAAC;SACxC;aAAM,IAAI,aAAa,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE;YAC3C,MAAM,GAAG,4CAA4C;gBACjD,gCAAgC,CAAC;SACtC;aAAM,IAAI,aAAa,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE;YAC3C,MAAM,GAAG,8CAA8C,CAAC;SACzD;KACF;IAED,OAAO,gBACE,QAAQ,oBACX,IAAI,4CACJ,aAAa,sCACS,cAAc,SAAI,qBAAqB,kBAC7D,MAAM,gBAEX,CAAC;AACJ,CAAC;AAED,SAAS,wBAAwB,CAC7B,SAAoB,EAAE,YAAuB;IAC/C,IAAM,OAAO,GAAG,SAAS,CAAC,IAAI,CAAC;IAC/B,IAAM,cAAc,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,GAAG,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;IAC1E,IAAM,QAAQ,GAAG,KAAK,GAAG,cAAc,GAAG,aAAa,CAAC;IACxD,IAAM,WAAW,GAAG,YAAY,CAAC,QAAQ,CAAC;IAC1C,IAAM,UAAU,GAAG,SAAS,CAAC,SAAS,CAAC,QAAQ,CAAC;IAChD,IAAM,MAAM,GAAG,SAAS,CAAC,SAAS,CAAC,YAAY,CAAC,MAAM,CAAC;IACvD,IAAM,OAAO,GAAG,YAAY,CAAC,YAAY,CAAC,MAAM,CAAC;IAEjD,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,SAAS,IAAI,MAAM,KAAK,OAAO;QACpD,SAAS,CAAC,SAAS,CAAC,UAAU,IAAI,IAAI;QACtC,IAAI,CAAC,WAAW,CAAC,UAAU,EAAE,WAAW,CAAC,EAAE;QAC7C,OAAO,mBACG,QAAQ,2CACS,OAAO,gCAEjC,CAAC;KACH;IAED,IAAM,IAAI,GAAG,iBAAiB,CAAC,OAAO,CAAC,CAAC;IACxC,IAAM,aAAa,GAAG,iCAAgB,CAClC,SAAS,CAAC,SAAS,CAAC,YAAY,EAAE,YAAY,CAAC,YAAY,CAAC,CAAC;IACjE,IAAM,QAAQ,GAAG,OAAO,GAAG,MAAM,CAAC;IAClC,IAAI,aAAqB,CAAC;IAC1B,IAAM,MAAM,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;IAE9C,IAAI,MAAM,KAAK,CAAC,EAAE;QAChB,aAAa,GAAG,EAAE,CAAC;KACpB;SAAM,IAAI,OAAO,GAAG,CAAC,IAAI,aAAa,CAAC,MAAM,IAAI,CAAC,EAAE;QACnD,aAAa,GAAG,aAAa,CAAC;KAC/B;SAAM;QACL,aAAa;YACT,aAAa,CAAC,GAAG,CAAC,UAAA,CAAC,IAAI,OAAA,YAAU,MAAM,CAAC,CAAC,GAAG,QAAQ,CAAC,UAAO,EAArC,CAAqC,CAAC;iBACxD,IAAI,CAAC,IAAI,CAAC,CAAC;KACrB;IACD,IAAI,qBAAqB,GAAG,EAAE,CAAC;IAC/B,IAAI,OAAO,GAAG,CAAC,IAAI,MAAM,GAAG,CAAC,EAAE;QAC7B,qBAAqB,GAAG,QAAQ,CAAC;KAClC;SAAM;QACL,qBAAqB,GAAG,SAAS,CAAC,SAAS,CAAC,YAAY;aAC3B,GAAG,CAAC,UAAC,CAAC,EAAE,CAAC,IAAK,OAAA,YAAU,MAAM,CAAC,CAAC,GAAG,QAAQ,CAAG,EAAhC,CAAgC,CAAC;aAC/C,IAAI,CAAC,IAAI,CAAC,CAAC;KACzC;IAED,OAAO,iBACG,QAAQ,oBACZ,IAAI,4CACJ,aAAa,0BACH,cAAc,SAAI,qBAAqB,kBAEtD,CAAC;AACJ,CAAC;AAED,SAAgB,iBAAiB,CAAC,IAAY;IAC5C,IAAI,IAAI,IAAI,CAAC,EAAE;QACb,OAAO,KAAK,CAAC;KACd;SAAM,IAAI,IAAI,KAAK,CAAC,EAAE;QACrB,OAAO,OAAO,CAAC;KAChB;SAAM,IAAI,IAAI,KAAK,CAAC,EAAE;QACrB,OAAO,OAAO,CAAC;KAChB;SAAM,IAAI,IAAI,KAAK,CAAC,EAAE;QACrB,OAAO,OAAO,CAAC;KAChB;SAAM,IAAI,IAAI,KAAK,CAAC,EAAE;QACrB,OAAO,OAAO,CAAC;KAChB;SAAM,IAAI,IAAI,KAAK,CAAC,EAAE;QACrB,OAAO,OAAO,CAAC;KAChB;SAAM;QACL,MAAM,KAAK,CAAC,kBAAgB,IAAI,0BAAuB,CAAC,CAAC;KAC1D;AACH,CAAC;AAhBD,8CAgBC;AAED,2EAA2E;AAC3E,SAAS,gBAAgB,CACrB,MAAiB,EAAE,aAAuB;IAC5C,aAAa;IACb,IAAM,YAAY,GAAc,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC;IACnE,YAAY,CAAC,SAAS,CAAC,YAAY,GAAG,aAAa,CAAC;IACpD,OAAO,YAAY,CAAC;AACtB,CAAC;AAED,SAAS,iBAAiB,CAAC,MAAgB,EAAE,QAAkB;IAC7D,OAAO,QAAQ,CAAC,GAAG,CAAC,UAAA,CAAC,IAAI,OAAA,MAAM,CAAC,CAAC,CAAC,EAAT,CAAS,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AACjD,CAAC"}