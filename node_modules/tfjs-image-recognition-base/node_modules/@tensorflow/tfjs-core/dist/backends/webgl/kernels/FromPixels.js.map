{"version": 3, "file": "FromPixels.js", "sourceRoot": "", "sources": ["../../../../src/backends/webgl/kernels/FromPixels.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;;GAeG;;AAEH,oDAAyC;AACzC,sDAAoF;AAIpF,wCAAyC;AAEzC,sEAAqE;AACrE,oFAAkF;AAErE,QAAA,gBAAgB,GAAiB;IAC5C,UAAU,EAAE,yBAAU;IACtB,WAAW,EAAE,OAAO;IACpB,UAAU,EAAE,UAA8B;CAC3C,CAAC;AAEF,IAAI,mBAA6C,CAAC;AAElD,SAAS,UAAU,CAAC,IAInB;IACQ,IAAA,oBAAM,EAAE,sBAAO,EAAE,kBAAK,CAAS;IACjC,IAAA,sBAAM,CAAW;IACf,IAAA,+BAAW,CAAU;IAE5B,IAAM,OAAO,GAAG,OAAO,CAAC,gBAAgB,CAAC,KAAK,WAAW;QACrD,MAAM,YAAY,gBAAgB,CAAC;IACvC,IAAM,OAAO,GAAG,OAAO,CAAC,gBAAgB,CAAC,KAAK,WAAW;QACrD,MAAM,YAAY,gBAAgB,CAAC;IACjC,IAAA;;;;;qCAK2B,EAL1B,aAAK,EAAE,cAKmB,CAAC;IAElC,IAAM,QAAQ,GAAqB,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;IACnD,IAAM,QAAQ,GAAG,CAAC,MAAM,EAAE,KAAK,EAAE,WAAW,CAAC,CAAC;IAE9C,IAAI,OAAO,IAAI,OAAO,EAAE;QACtB,IAAI,mBAAmB,IAAI,IAAI,EAAE;YAC/B,mBAAmB,GAAG,QAAQ,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;SACzE;QAED,mBAAmB,CAAC,MAAM,CAAC,KAAK,GAAG,KAAK,CAAC;QACzC,mBAAmB,CAAC,MAAM,CAAC,MAAM,GAAG,MAAM,CAAC;QAC3C,mBAAmB,CAAC,SAAS,CACzB,MAA6C,EAAE,CAAC,EAAE,CAAC,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC;QACxE,MAAM,GAAG,mBAAmB,CAAC,MAAM,CAAC;KACrC;IAED,IAAM,eAAe,GAAG,OAAO,CAAC,cAAc,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;IAClE,sCAAsC;IACtC,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC,KAAK,GAAG,uBAAY,CAAC,MAAM,CAAC;IACxE,OAAO,CAAC,KAAK,CAAC,wBAAwB,CAClC,OAAO,CAAC,UAAU,CAAC,eAAe,CAAC,MAAM,CAAC,EAAE,MAAmB,CAAC,CAAC;IACrE,IAAM,OAAO,GAAG,iBAAG,EAAE,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,CAAC;QACzC,IAAI,gDAAuB,CAAC,QAAQ,CAAC,CAAC,CAAC;QACvC,IAAI,mCAAiB,CAAC,QAAQ,CAAC,CAAC;IACpC,IAAM,GAAG,GAAG,OAAO,CAAC,eAAe,CAAC,OAAO,EAAE,CAAC,eAAe,CAAC,EAAE,OAAO,CAAC,CAAC;IACzE,OAAO,CAAC,WAAW,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC;IAC5C,OAAO,GAAG,CAAC;AACb,CAAC"}