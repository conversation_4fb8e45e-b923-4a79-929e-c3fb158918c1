{"version": 3, "file": "transpose_packed_gpu.js", "sourceRoot": "", "sources": ["../../../src/backends/webgl/transpose_packed_gpu.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;;GAeG;;AAEH,gDAA+C;AAG/C,qDAAoD;AAEpD;IAQE,gCAAY,MAAgB,EAAE,MAAgB;QAP9C,kBAAa,GAAG,CAAC,GAAG,CAAC,CAAC;QAItB,iBAAY,GAAG,IAAI,CAAC;QACpB,iBAAY,GAAG,IAAI,CAAC;QAGlB,IAAM,WAAW,GAAa,IAAI,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;QACvD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,WAAW,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YAC3C,WAAW,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;SACpC;QACD,IAAI,CAAC,WAAW,GAAG,WAAW,CAAC;QAC/B,IAAI,CAAC,IAAI,GAAG,WAAW,CAAC,MAAM,CAAC;QAC/B,IAAI,IAAI,CAAC,IAAI,GAAG,CAAC,EAAE;YACjB,MAAM,KAAK,CACP,+BAA6B,IAAI,CAAC,IAAI,2BAAwB,CAAC,CAAC;SACrE;QACD,IAAM,KAAK,GAAG,mCAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAE3C,IAAM,WAAW,GAAG,6BAAc,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;QACpD,IAAM,aAAa,GAAG,IAAI,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC3C,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACtC,aAAa,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC;SAC3C;QACD,IAAM,SAAS,GAAG,UAAQ,aAAa,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,MAAG,CAAC;QAC5D,IAAM,UAAU,GACZ,OAAK,WAAW,CAAC,IAAI,CAAC,IAAI,GAAG,CAAC,CAAC,WAAM,WAAW,CAAC,IAAI,CAAC,IAAI,GAAG,CAAC,CAAG,CAAC;QACtE,IAAM,IAAI,GAAG,qBAAmB,aAAa,CAAC,IAAI,EAAE,WAAM,SAAS,MAAG,CAAC;QAEvE,IAAI,CAAC,QAAQ,GAAG,gCAEZ,KAAK,mFAEO,IAAI,oBACb,UAAU,iCACC,IAAI,4BAEhB,WAAW,CAAC,IAAI,CAAC,IAAI,GAAG,CAAC,CAAC,sBACvB,WAAW,CAAC,IAAI,CAAC,IAAI,GAAG,CAAC,CAAC,WAAM,WAAW,CAAC,IAAI,CAAC,IAAI,GAAG,CAAC,CAAC,iCACjD,IAAI,sBACb,UAAU,mCACC,IAAI,iEAKvB,CAAC;IACJ,CAAC;IACH,6BAAC;AAAD,CAAC,AAlDD,IAkDC;AAlDY,wDAAsB"}