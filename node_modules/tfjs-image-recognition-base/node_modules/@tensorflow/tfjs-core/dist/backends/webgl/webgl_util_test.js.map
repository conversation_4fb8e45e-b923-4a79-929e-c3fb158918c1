{"version": 3, "file": "webgl_util_test.js", "sourceRoot": "", "sources": ["../../../src/backends/webgl/webgl_util_test.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;;GAeG;;AAEH,gCAAkC;AAClC,mDAAqD;AACrD,iCAAmC;AACnC,6EAAyD;AACzD,yCAA2C;AAE3C,gCAAiB,CAAC,iCAAiC,EAAE,wCAAU,EAAE;IAC/D,EAAE,CAAC,QAAQ,EAAE;QACX,IAAM,QAAQ,GAAG,UAAU,CAAC,+BAA+B,CAAC,EAAE,CAAC,CAAC;QAChE,MAAM,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACnC,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,IAAI,EAAE;QACP,IAAM,QAAQ,GAAG,UAAU,CAAC,+BAA+B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACjE,MAAM,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACnC,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,eAAe,EAAE;QAClB,IAAI,QAAQ,GAAG,UAAU,CAAC,+BAA+B,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QAClE,MAAM,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QAEjC,QAAQ,GAAG,UAAU,CAAC,+BAA+B,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QAC9D,MAAM,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QAEjC,QAAQ,GAAG,UAAU,CAAC,+BAA+B,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QAC9D,MAAM,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACnC,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,UAAU,EAAE;QACb,IAAM,QAAQ,GAAG,UAAU,CAAC,+BAA+B,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACvE,MAAM,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACnC,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,cAAc,EAAE;QACjB,IAAM,QAAQ,GAAG,UAAU,CAAC,+BAA+B,CAAC,CAAC,CAAC,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC;QAC3E,MAAM,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC;IAC3C,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,uBAAuB,EAAE;QAC1B,IAAM,QAAQ,GAAG,UAAU,CAAC,+BAA+B,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACvE,MAAM,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACnC,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,uBAAuB,EAAE;QAC1B,IAAM,QAAQ,GAAG,UAAU,CAAC,+BAA+B,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACvE,MAAM,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACnC,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,6BAA6B,EAAE;QAChC,IAAM,QAAQ,GACV,UAAU,CAAC,+BAA+B,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC;QACjE,MAAM,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC;IAC/C,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,yBAAyB,EAAE;QAC5B,IAAM,QAAQ,GAAG,UAAU,CAAC,+BAA+B,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QAC1E,MAAM,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACnC,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,yBAAyB,EAAE;QAC5B,IAAM,QAAQ,GAAG,UAAU,CAAC,+BAA+B,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QAC1E,MAAM,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACnC,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC;AAEH,gCAAiB,CAAC,wCAAwC,EAAE,wCAAU,EAAE;IACtE,EAAE,CAAC,kEAAkE,EAAE;QACrE,IAAM,QAAQ,GAAG,IAAI,CAAC;QACtB,IAAM,YAAY,GAAG;YACnB,CAAC;YACD,IAAI,CAAC,iBAAiB,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,SAAS,CAAC,wBAAwB,CAAC,GAAG,CAAC,CAAC;SACzE,CAAC;QACF,IAAM,QAAQ,GACV,UAAU,CAAC,+BAA+B,CAAC,YAAY,EAAE,QAAQ,CAAC,CAAC;QACvE,MAAM,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;IACzC,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,kCAAkC,EAAE;QACrC,IAAM,QAAQ,GAAG,IAAI,CAAC;QACtB,IAAM,YAAY,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QAC/B,IAAM,QAAQ,GACV,UAAU,CAAC,+BAA+B,CAAC,YAAY,EAAE,QAAQ,CAAC,CAAC;QACvE,MAAM,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACnC,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,2DAA2D,EAAE;QAC9D,IAAM,QAAQ,GAAG,IAAI,CAAC;QACtB,IAAM,GAAG,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,SAAS,CAAC,wBAAwB,CAAC,CAAC;QAEzD,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,wBAAwB,EAAE,CAAC,CAAC,CAAC;QAC1C,IAAM,YAAY,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;QAC7B,IAAM,QAAQ,GACV,UAAU,CAAC,+BAA+B,CAAC,YAAY,EAAE,QAAQ,CAAC,CAAC;QAEvE,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,wBAAwB,EAAE,GAAG,CAAC,CAAC;QAC5C,MAAM,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACnC,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC;AAEH,gCAAiB,CAAC,eAAe,EAAE,wCAAU,EAAE;IAC7C,EAAE,CAAC,oDAAoD,EAAE;QACvD,IAAM,MAAM,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QACzB,IAAM,KAAK,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QACxB,MAAM,CAAC,UAAU,CAAC,aAAa,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAC7D,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,4CAA4C,EAAE;QAC/C,IAAM,MAAM,GAAa,EAAE,CAAC;QAC5B,IAAM,KAAK,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QACxB,MAAM,CAAC,UAAU,CAAC,aAAa,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAC7D,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,6CAA6C,EAAE;QAChD,IAAM,MAAM,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACtB,IAAM,KAAK,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QACxB,MAAM,CAAC,UAAU,CAAC,aAAa,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAC7D,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,mEAAmE,EACnE;QACE,IAAM,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC;QACnB,IAAM,KAAK,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QACxB,MAAM,CAAC,UAAU,CAAC,aAAa,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAC7D,CAAC,CAAC,CAAC;IAEN,EAAE,CAAC,4DAA4D;QACxD,kDAAkD,EACtD;QACE,IAAM,MAAM,GAAG,CAAC,EAAE,CAAC,CAAC;QACpB,IAAM,KAAK,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QACxB,MAAM,CAAC,UAAU,CAAC,aAAa,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAC7D,CAAC,CAAC,CAAC;IAEN,EAAE,CAAC,iEAAiE;QAC7D,+BAA+B,EACnC;QACE,IAAM,MAAM,GAAG,CAAC,EAAE,CAAC,CAAC;QACpB,IAAM,KAAK,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QACxB,MAAM,CAAC,UAAU,CAAC,aAAa,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IAC9D,CAAC,CAAC,CAAC;IAEN,EAAE,CAAC,uEAAuE,EACvE;QACE,IAAM,MAAM,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QACzB,IAAM,KAAK,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QACxB,MAAM,CAAC,UAAU,CAAC,aAAa,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAC7D,CAAC,CAAC,CAAC;IAEN,EAAE,CAAC,8DAA8D,EAAE;QACjE,IAAM,MAAM,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QACzB,IAAM,KAAK,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC;QACzB,MAAM,CAAC,UAAU,CAAC,aAAa,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IAC9D,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,sEAAsE,EACtE;QACE,IAAM,MAAM,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QACzB,IAAM,KAAK,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QACxB,MAAM,CAAC,UAAU,CAAC,aAAa,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IAC9D,CAAC,CAAC,CAAC;AACR,CAAC,CAAC,CAAC"}