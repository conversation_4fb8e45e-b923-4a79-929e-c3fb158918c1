{"version": 3, "file": "webgl_batchnorm_test.js", "sourceRoot": "", "sources": ["../../../src/backends/webgl/webgl_batchnorm_test.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;;GAeG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEH,iBAgEA;;AAhEA,gCAAkC;AAClC,mDAAqD;AACrD,6CAAkD;AAClD,6EAAsE;AAEtE,gCAAiB,CAAC,WAAW,EAAE,wCAAU,EAAE;IACzC,EAAE,CAAC,oCAAoC,EAAE;;;;;oBACjC,CAAC,GAAG,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;oBAC7C,IAAI,GAAG,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;oBACtC,QAAQ,GAAG,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;oBAE1C,MAAM,GAAG,EAAE,CAAC,WAAW,CAAC,CAAC,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAC;oBACjD,KAAA,6BAAiB,CAAA;oBACb,qBAAM,MAAM,CAAC,IAAI,EAAE,EAAA;;oBADvB,kBACI,SAAmB,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,UAAU,CAAC,EAAC,CAAC;;;;SACzE,CAAC,CAAC;IAEH,EAAE,CAAC,yDAAyD,EAAE;;;;;oBACtD,cAAc,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,SAAS,CAAC,wBAAwB,CAAC,CAAC;oBACpE,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,wBAAwB,EAAE,CAAC,CAAC,CAAC;oBAEpC,CAAC,GAAG,EAAE,CAAC,QAAQ,CACjB;wBACE,UAAU,EAAE,UAAU,EAAE,CAAC,UAAU,EAAE,UAAU,EAAE,CAAC,UAAU;wBAC5D,UAAU,EAAE,UAAU,EAAE,QAAQ,EAAE,UAAU,EAAE,UAAU,EAAE,CAAC,UAAU;wBACrE,UAAU,EAAE,CAAC,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU;wBAC3D,UAAU,EAAE,UAAU;qBACvB,EACD,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;oBACT,IAAI,GAAG,EAAE,CAAC,QAAQ,CAAC,CAAC,UAAU,EAAE,CAAC,UAAU,EAAE,SAAS,CAAC,CAAC,CAAC;oBACzD,QAAQ,GAAG,EAAE,CAAC,QAAQ,CAAC,CAAC,UAAU,EAAE,UAAU,EAAE,UAAU,CAAC,CAAC,CAAC;oBAC7D,MAAM,GAAG,EAAE,CAAC,QAAQ,CAAC,CAAC,UAAU,EAAE,CAAC,UAAU,EAAE,SAAS,CAAC,CAAC,CAAC;oBAC3D,KAAK,GAAG,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC,SAAS,EAAE,SAAS,EAAE,UAAU,CAAC,CAAC,CAAC;oBACzD,eAAe,GAAG,IAAI,CAAC;oBAEvB,MAAM,GACR,EAAE,CAAC,WAAW,CAAC,CAAC,EAAE,IAAI,EAAE,QAAQ,EAAE,MAAM,EAAE,KAAK,EAAE,eAAe,CAAC,CAAC;oBAEtE,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,wBAAwB,EAAE,cAAc,CAAC,CAAC;oBAEvD,KAAA,6BAAiB,CAAA;oBAAC,qBAAM,MAAM,CAAC,IAAI,EAAE,EAAA;;oBAArC,kBAAkB,SAAmB,EAAE;4BACrC,UAAU,EAAE,CAAC,UAAU,EAAE,SAAS,EAAE,CAAC,UAAU,EAAE,CAAC,UAAU,EAAE,UAAU;4BACxE,CAAC,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,CAAC,UAAU,EAAE,CAAC,UAAU,EAAE,UAAU;4BACzE,UAAU,EAAE,CAAC,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,SAAS;yBACvE,EAAC,CAAC;;;;SACJ,CAAC,CAAC;AACL,CAAC,CAAC,CAAC;AAEH,gCAAiB,CAAC,kBAAkB,EAAE,yCAAW,EAAE;IACjD,EAAE,CAAC,wBAAwB,EAAE;QAC3B,IAAM,CAAC,GAAG,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACnD,IAAM,IAAI,GAAG,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACjC,IAAM,QAAQ,GAAG,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACrC,IAAM,eAAe,GAAG,IAAI,CAAC;QAE7B,IAAM,aAAa,GAAG,EAAE,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC;QAC3C,IAAM,eAAe,GAAG,EAAE,CAAC,MAAM,EAAE,CAAC,UAAU,CAAC;QAC/C,EAAE,CAAC,WAAW,CAAC,CAAC,EAAE,IAAI,EAAE,QAAQ,EAAE,SAAS,EAAE,SAAS,EAAE,eAAe,CAAC,CAAC;QACzE,IAAM,WAAW,GAAG,EAAE,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC;QACzC,IAAM,aAAa,GAAG,EAAE,CAAC,MAAM,EAAE,CAAC,UAAU,CAAC;QAE7C,MAAM,CAAC,WAAW,GAAG,aAAa,CAAC,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;QAChD,MAAM,CAAC,aAAa,GAAG,eAAe,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IACrD,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC"}