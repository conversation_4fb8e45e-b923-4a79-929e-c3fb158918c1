{"version": 3, "file": "shader_compiler_util.js", "sourceRoot": "", "sources": ["../../../src/backends/webgl/shader_compiler_util.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;;GAeG;;AAEH,iCAAmC;AAEnC;;;;GAIG;AACH,SAAgB,kCAAkC,CAC9C,MAAgB,EAAE,KAAe,EAAE,KAAe;IAAf,sBAAA,EAAA,eAAe;IACpD,IAAM,OAAO,GAAG,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;IAC3C,OAAO,OAAO;SACT,GAAG,CAAC,UAAC,MAAM,EAAE,CAAC;QACb,IAAM,KAAK,GAAG,SAAO,MAAM,CAAC,CAAC,CAAC,WAAM,KAAK,WAAM,MAAQ,CAAC;QACxD,IAAM,KAAK,GAAG,CAAC,KAAK,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;YACpC,SAAO,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,WAAM,KAAK,WAAM,MAAM,CAAC,CAAC,CAAC,WAAM,MAAQ,CAAC,CAAC;YAC9D,cAAY,MAAM,CAAC,CAAC,CAAC,WAAM,MAAQ,CAAC;QACxC,OAAU,KAAK,UAAK,KAAK,MAAG,CAAC;IAC/B,CAAC,CAAC;SACD,IAAI,CAAC,EAAE,CAAC,CAAC;AAChB,CAAC;AAZD,gFAYC;AAED,SAAS,QAAQ,CAAC,CAAW;IAC3B,IAAI,CAAC,CAAC,MAAM,KAAK,CAAC,EAAE;QAClB,OAAO,KAAG,CAAC,CAAC,CAAC,CAAG,CAAC;KAClB;IACD,OAAO,QAAM,CAAC,CAAC,MAAM,SAAI,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,MAAG,CAAC;AAC1C,CAAC;AAED;;;GAGG;AACH,SAAgB,MAAM,CAAC,CAAW,EAAE,CAAW;IAC7C,IAAI,CAAC,CAAC,MAAM,KAAK,CAAC,CAAC,MAAM,EAAE;QACzB,MAAM,IAAI,KAAK,CACX,mDAAmD;aACnD,SAAO,CAAC,CAAC,MAAM,aAAQ,CAAC,CAAC,MAAQ,CAAA,CAAC,CAAC;KACxC;IAED,IAAM,MAAM,GAAa,EAAE,CAAC;IAC5B,IAAM,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;IAC7C,IAAM,oBAAoB,GAAG,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC;IAE1C,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,WAAW,EAAE,CAAC,EAAE,EAAE;QACpC,IAAM,MAAM,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;QACzC,IAAM,MAAM,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;QACzC,MAAM,CAAC,IAAI,CAAI,QAAQ,CAAC,MAAM,CAAC,UAAK,QAAQ,CAAC,MAAM,CAAG,CAAC,CAAC;KACzD;IAED,IAAI,oBAAoB,KAAK,CAAC,EAAE;QAC9B,IAAI,MAAM,GAAG,CAAC,CAAC,KAAK,CAAC,WAAW,GAAG,CAAC,CAAC,CAAC;QACtC,IAAI,MAAM,GAAG,CAAC,CAAC,KAAK,CAAC,WAAW,GAAG,CAAC,CAAC,CAAC;QACtC,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE;YACvB,MAAM,GAAG,MAAM,CAAC,GAAG,CAAC,UAAA,CAAC,IAAI,OAAA,WAAS,CAAC,MAAG,EAAb,CAAa,CAAC,CAAC;YACxC,MAAM,GAAG,MAAM,CAAC,GAAG,CAAC,UAAA,CAAC,IAAI,OAAA,WAAS,CAAC,MAAG,EAAb,CAAa,CAAC,CAAC;SACzC;QACD,MAAM,CAAC,IAAI,CAAI,QAAQ,CAAC,MAAM,CAAC,UAAK,QAAQ,CAAC,MAAM,CAAG,CAAC,CAAC;KACzD;IAED,OAAO,MAAM,CAAC,GAAG,CAAC,UAAC,CAAC,EAAE,CAAC,IAAK,OAAA,SAAO,CAAC,MAAG,EAAX,CAAW,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AACrD,CAAC;AA5BD,wBA4BC;AAED;;GAEG;AACH,SAAgB,kBAAkB,CAAC,KAA+B;IAChE,IAAM,OAAO,GAAG,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,QAAQ,EAAE,EAAZ,CAAY,CAAC,CAAC;IAElE,OAAO,iEAEe,OAAO,CAAC,CAAC,CAAC,sBAAiB,OAAO,CAAC,CAAC,CAAC,wBAE5D,CAAC;AACF,CAAC;AARD,gDAQC;AAEY,QAAA,oBAAoB,GAAG,y7BAuCnC,CAAC"}