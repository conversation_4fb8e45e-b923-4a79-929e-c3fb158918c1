{"version": 3, "file": "pad_packed_gpu.js", "sourceRoot": "", "sources": ["../../../src/backends/webgl/pad_packed_gpu.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;;GAeG;;AAEH,gDAA4C;AAG5C,qDAAoD;AAEpD;IAOE,0BACI,MAAgB,EAAE,QAAiC,EACnD,aAAqB;QARzB,kBAAa,GAAG,CAAC,GAAG,CAAC,CAAC;QACtB,iBAAY,GAAG,IAAI,CAAC;QACpB,iBAAY,GAAG,IAAI,CAAC;QAOlB,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,GAAG,CAC3B,UAAC,CAAC,EAAE,CAAC,IAAK,OAAA,CAAC,CAAC,CAAC,CAAC,CAAC,eAAe,GAAG,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAvC,CAAuC,CAAC,cAAc,CAAC,CAAC;QACtE,IAAM,IAAI,GAAG,MAAM,CAAC,MAAM,CAAC;QAC3B,IAAM,KAAK,GAAG,mCAAiB,CAAC,IAAI,CAAC,CAAC;QAEtC,IAAM,KAAK,GAAG,QAAQ,CAAC,GAAG,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,CAAC,CAAC,EAAJ,CAAI,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QAChD,IAAM,GAAG,GAAG,QAAQ,CAAC,GAAG,CAAC,UAAC,CAAC,EAAE,CAAC,IAAK,OAAA,CAAC,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,EAAhB,CAAgB,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QAC/D,IAAM,MAAM,GAAG,0BAAW,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;QACvC,IAAM,MAAM,GAAG,0BAAW,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;QAC3C,IAAM,MAAM,GAAM,MAAM,CAAC,IAAI,GAAG,CAAC,CAAC,WAAM,IAAI,CAAC,WAAW,CAAC,IAAI,GAAG,CAAC,CAAG,CAAC;QACrE,IAAM,SAAS,GACX,IAAI,KAAK,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,UAAQ,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,MAAG,CAAC;QAE/D,IAAM,cAAc,GAAG;YAClB,KAAK,qBAAkB,EAAK,MAAM,CAAC,IAAI,GAAG,CAAC,CAAC,0BACzC,MAAM,gBACX;YACD,IAAI,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,uCAEf,MAAM,CAAC,IAAI,GAAG,CAAC,CAAC,0BACb,MAAM,CAAC,IAAI,GAAG,CAAC,CAAC,WAAM,IAAI,CAAC,WAAW,CAAC,IAAI,GAAG,CAAC,CAAC,QAAK;YAC3D,IAAI,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAK,MAAM,CAAC,IAAI,GAAG,CAAC,CAAC,4BAC/B,MAAM,QAAK;SACpB,CAAC;QAEF,IAAM,WAAW,GAAG,IAAI,KAAK,CAAC,CAAC,CAAC;YAC5B,yBAAyB,CAAC,CAAC;YAC3B,4DAA4D,CAAC;QACjE,IAAI,QAAQ,GAAG,EAAE,CAAC;QAClB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;YAClD,QAAQ,IAAI,eACR,cAAc,CAAC,CAAC,CAAC,sBACb,WAAW,8BACN,CAAC,kBAAa,aAAa,wCAElC,KAAK,gDACE,CAAC,4BAAuB,MAAM,CAAC,IAAI,EAAE,WAAM,SAAS,0BAEhE,CAAC;SACH;QACD,QAAQ,IAAI,CAAC,IAAI,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;QAEvC,IAAI,CAAC,QAAQ,GAAG,mBACN,KAAK,iBAAY,KAAK,SAAI,KAAK,wBAC/B,KAAK,eAAU,KAAK,SAAI,GAAG,2CAG/B,KAAK,kFAEL,QAAQ,gDAGb,CAAC;IACJ,CAAC;IACH,uBAAC;AAAD,CAAC,AAhED,IAgEC;AAhEY,4CAAgB"}