"use strict";
/**
 * @license
 * Copyright 2020 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 * =============================================================================
 */
Object.defineProperty(exports, "__esModule", { value: true });
var pool_gpu_1 = require("../pool_gpu");
function maxPoolWithArgmaxImpl(x, includeBatchInIndex, convInfo, backend) {
    var program = new pool_gpu_1.Pool2DProgram(convInfo, 'max', false);
    var poolOutput = backend.runWebGLProgram(program, [x], 'float32');
    program = new pool_gpu_1.Pool2DProgram(convInfo, 'max', true, true, includeBatchInIndex);
    var indexOutput = backend.runWebGLProgram(program, [x], 'float32');
    return [poolOutput, indexOutput];
}
exports.maxPoolWithArgmaxImpl = maxPoolWithArgmaxImpl;
//# sourceMappingURL=MaxPoolWithArgmax_impl.js.map