/**
 * @license
 * Copyright 2017 Google Inc. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 * =============================================================================
 */
import { GPGPUProgram } from './gpgpu_math';
export declare class UnaryOpProgram implements GPGPUProgram {
    variableNames: string[];
    userCode: string;
    outputShape: number[];
    constructor(aShape: number[], opSnippet: string);
}
export declare const LINEAR = "return x;";
export declare const ABS = "return abs(x);";
export declare const RELU: string;
export declare const RELU6: string;
export declare const ELU = "return (x >= 0.0) ? x : (exp(x) - 1.0);";
export declare const SELU: string;
export declare function STEP(alpha?: number): string;
export declare const NEG = "return -x;";
export declare const CEIL = "return ceil(x);";
export declare const FLOOR = "return floor(x);";
export declare const SIGN = "\n  if (isnan(x)) { return 0.0; }\n  return sign(x);\n";
export declare const IS_NAN = "return float(isnan(x));";
export declare const IS_INF = "return float(isinf(x));";
export declare const IS_FINITE = "return float(!isnan(x) && !isinf(x));";
export declare const ROUND = "\n  // OpenGL ES does not support round function.\n  // The algorithm is based on banker's rounding.\n  float base = floor(x);\n  if ((x - base) < 0.5) {\n    return floor(x);\n  } else if ((x - base) > 0.5) {\n    return ceil(x);\n  } else {\n    if (mod(base, 2.0) == 0.0) {\n      return base;\n    } else {\n      return base + 1.0;\n    }\n  }\n";
export declare const EXP = "return exp(x);";
export declare const EXPM1 = "return exp(x) - 1.0;";
export declare const LOG = "if (x < 0.0) return NAN;\n  return log(x);";
export declare const LOG1P = "return log(1.0 + x);";
export declare const SQRT = "return sqrt(x);";
export declare const RSQRT = "return inversesqrt(x);";
export declare const SIGMOID = "return 1.0 / (1.0 + exp(-1.0 * x));";
/**
 * mirrors the implementation of tf.nn.softplus: https://goo.gl/vkcvwX
 *
 * epsilon is the difference between 1.0 and the next representable
 * float. For a single precision 32 bit float this should be 2^-23, see:
 * https://math.byu.edu/~schow/work/IEEEFloatingPoint.htm
 *
 * too_large = (x > -threshold) is value above which exp(x) may overflow
 * but softplus(x) == x is within machine epsilon
 *
 * too_small = (x < threshold) is value below which exp(x) may underflow,
 * but softplus(x) == exp(x) is within machine epsilon.
 */
export declare const SOFTPLUS = "\n  float epsilon = 1.1920928955078125e-7;\n  float threshold = log(epsilon) + 2.0;\n\n  bool too_large = x > -threshold;\n  bool too_small = x < threshold;\n\n  float result;\n  float exp_x = exp(x);\n\n  if (too_large){\n    result = x;\n  }\n  else if (too_small){\n    result = exp_x;\n  }\n  else{\n    result = log(exp_x + 1.0);\n  }\n  return result;\n";
export declare const SIN: string;
export declare const COS: string;
export declare const TAN = "return tan(x);";
export declare const ASIN: string;
export declare const ACOS: string;
export declare const ATAN: string;
export declare const SINH = "\n  float e2x = exp(x);\n  return (e2x - 1.0 / e2x) / 2.0;\n";
export declare const COSH = "\n  float e2x = exp(-x);\n  return (e2x + 1.0 / e2x) / 2.0;\n";
export declare const TANH = "\n  float e2x = exp(-2.0 * abs(x));\n  return sign(x) * (1.0 - e2x) / (1.0 + e2x);\n";
export declare const ASINH: string;
export declare const ACOSH: string;
export declare const ATANH: string;
export declare const ERF: string;
export declare const SQUARE = "return x * x;";
export declare const RECIPROCAL = "return 1.0 / x;";
export declare const LOGICAL_NOT = "return float(!(x >= 1.0));";
export declare const TO_INT = "return float(int(x));";
export declare const CLONE = "return x;";
