{"version": 3, "file": "texture_manager.js", "sourceRoot": "", "sources": ["../../../src/backends/webgl/texture_manager.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;;GAeG;;AAEH,iDAAsC;AAGtC,uCAA6D;AAE7D;IAOE,wBAAoB,KAAmB;QAAnB,UAAK,GAAL,KAAK,CAAc;QAN/B,oBAAe,GAAG,CAAC,CAAC;QACpB,oBAAe,GAAG,CAAC,CAAC;QACpB,iBAAY,GAAsC,EAAE,CAAC;QACrD,eAAU,GAAG,KAAK,CAAC;QACnB,iBAAY,GAAsC,EAAE,CAAC;IAEnB,CAAC;IAE3C,uCAAc,GAAd,UACI,OAAyB,EAAE,KAAmB,EAC9C,QAAiB;QACnB,IAAM,eAAe,GAAG,iCAAiC,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC;QAE3E,IAAM,QAAQ,GAAG,sBAAsB,CAAC,OAAO,EAAE,eAAe,EAAE,QAAQ,CAAC,CAAC;QAC5E,IAAI,CAAC,CAAC,QAAQ,IAAI,IAAI,CAAC,YAAY,CAAC,EAAE;YACpC,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,GAAG,EAAE,CAAC;SAClC;QACD,IAAI,CAAC,CAAC,QAAQ,IAAI,IAAI,CAAC,YAAY,CAAC,EAAE;YACpC,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,GAAG,EAAE,CAAC;SAClC;QAED,IAAI,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC,MAAM,GAAG,CAAC,EAAE;YAC1C,IAAI,CAAC,eAAe,EAAE,CAAC;YACvB,IAAI,CAAC,eAAe,EAAE,CAAC;YACvB,IAAI,CAAC,GAAG,EAAE,CAAC;YACX,IAAM,YAAU,GAAG,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC,KAAK,EAAE,CAAC;YACvD,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,YAAU,CAAC,CAAC;YAC7C,OAAO,YAAU,CAAC;SACnB;QACD,IAAI,CAAC,eAAe,EAAE,CAAC;QACvB,IAAI,CAAC,GAAG,EAAE,CAAC;QAEX,IAAI,UAAwB,CAAC;QAC7B,IAAI,eAAe,KAAK,8BAAmB,CAAC,kBAAkB,EAAE;YAC9D,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,yBAAyB,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;SAC3E;aAAM,IAAI,eAAe,KAAK,8BAAmB,CAAC,kBAAkB,EAAE;YACrE,UAAU;gBACN,IAAI,CAAC,KAAK,CAAC,gCAAgC,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;SACzE;aAAM,IAAI,eAAe,KAAK,8BAAmB,CAAC,gBAAgB,EAAE;YACnE,UAAU;gBACN,IAAI,CAAC,KAAK,CAAC,0BAA0B,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;SACnE;aAAM,IAAI,eAAe,KAAK,8BAAmB,CAAC,gBAAgB,EAAE;YACnE,UAAU;gBACN,IAAI,CAAC,KAAK,CAAC,0BAA0B,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;SAEnE;aAAM,IACH,eAAe,KAAK,8BAAmB,CAAC,wBAAwB,EAAE;YACpE,UAAU;gBACN,IAAI,CAAC,KAAK,CAAC,gCAAgC,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;SACzE;QACD,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QAE7C,OAAO,UAAU,CAAC;IACpB,CAAC;IAED,uCAAc,GAAd,UACI,OAAqB,EAAE,KAAuB,EAC9C,cAA4B,EAAE,QAAiB;QACjD,IAAI,IAAI,CAAC,YAAY,IAAI,IAAI,EAAE;YAC7B,oBAAoB;YACpB,OAAO;SACR;QACD,IAAM,eAAe,GACjB,iCAAiC,CAAC,cAAc,EAAE,QAAQ,CAAC,CAAC;QAChE,IAAM,QAAQ,GAAG,sBAAsB,CAAC,KAAK,EAAE,eAAe,EAAE,QAAQ,CAAC,CAAC;QAC1E,IAAI,CAAC,CAAC,QAAQ,IAAI,IAAI,CAAC,YAAY,CAAC,EAAE;YACpC,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,GAAG,EAAE,CAAC;SAClC;QACD,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAC1C,IAAI,CAAC,eAAe,EAAE,CAAC;QACvB,IAAI,CAAC,eAAe,EAAE,CAAC;QACvB,IAAM,OAAO,GAAG,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;QAC5C,IAAM,QAAQ,GAAG,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;QAC1C,IAAI,QAAQ,GAAG,CAAC,EAAE;YAChB,MAAM,IAAI,KAAK,CACX,2DAA2D;gBAC3D,iBAAiB,CAAC,CAAC;SACxB;QACD,OAAO,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC;QAC5B,IAAI,CAAC,GAAG,EAAE,CAAC;IACb,CAAC;IAEO,4BAAG,GAAX;QACE,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;YACpB,OAAO;SACR;QACD,IAAM,KAAK,GAAG,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,eAAe,CAAC;QAC1D,OAAO,CAAC,GAAG,CACP,WAAW,EAAK,IAAI,CAAC,eAAe,WAAM,IAAI,CAAC,eAAiB,EAChE,MAAI,KAAK,MAAG,CAAC,CAAC;IACpB,CAAC;IAED,2CAAkB,GAAlB;QACE,OAAO,IAAI,CAAC,eAAe,CAAC;IAC9B,CAAC;IAED,2CAAkB,GAAlB;QACE,OAAO,IAAI,CAAC,eAAe,CAAC;IAC9B,CAAC;IAED,gCAAO,GAAP;QAAA,iBAmBC;QAlBC,IAAI,IAAI,CAAC,YAAY,IAAI,IAAI,EAAE;YAC7B,oBAAoB;YACpB,OAAO;SACR;QACD,KAAK,IAAM,QAAQ,IAAI,IAAI,CAAC,YAAY,EAAE;YACxC,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,UAAA,GAAG;gBACrC,KAAI,CAAC,KAAK,CAAC,mBAAmB,CAAC,GAAG,CAAC,CAAC;YACtC,CAAC,CAAC,CAAC;SACJ;QACD,KAAK,IAAM,QAAQ,IAAI,IAAI,CAAC,YAAY,EAAE;YACxC,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,UAAA,GAAG;gBACrC,KAAI,CAAC,KAAK,CAAC,mBAAmB,CAAC,GAAG,CAAC,CAAC;YACtC,CAAC,CAAC,CAAC;SACJ;QACD,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;QACzB,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;QACzB,IAAI,CAAC,eAAe,GAAG,CAAC,CAAC;QACzB,IAAI,CAAC,eAAe,GAAG,CAAC,CAAC;IAC3B,CAAC;IACH,qBAAC;AAAD,CAAC,AAzHD,IAyHC;AAzHY,wCAAc;AA2H3B,SAAS,8BAA8B,CAAC,QAAiB;IAEvD,IAAI,iBAAG,EAAE,CAAC,OAAO,CAAC,8BAA8B,CAAC,EAAE;QACjD,IAAI,QAAQ,EAAE;YACZ,OAAO,8BAAmB,CAAC,kBAAkB,CAAC;SAC/C;QACD,OAAO,8BAAmB,CAAC,gBAAgB,CAAC;KAC7C;IAED,IAAI,QAAQ,EAAE;QACZ,OAAO,8BAAmB,CAAC,kBAAkB,CAAC;KAC/C;IACD,OAAO,8BAAmB,CAAC,gBAAgB,CAAC;AAC9C,CAAC;AAED,SAAS,iCAAiC,CACtC,cAA4B,EAAE,QAAiB;IACjD,IAAI,cAAc,KAAK,uBAAY,CAAC,MAAM,EAAE;QAC1C,OAAO,8BAAmB,CAAC,kBAAkB,CAAC;KAC/C;SAAM,IAAI,cAAc,KAAK,uBAAY,CAAC,MAAM,IAAI,cAAc,IAAI,IAAI,EAAE;QAC3E,OAAO,8BAA8B,CAAC,QAAQ,CAAC,CAAC;KACjD;SAAM,IACH,cAAc,KAAK,uBAAY,CAAC,QAAQ;QACxC,cAAc,KAAK,uBAAY,CAAC,MAAM,EAAE;QAC1C,OAAO,8BAAmB,CAAC,wBAAwB,CAAC;KACrD;IACD,MAAM,IAAI,KAAK,CAAC,kCAAgC,cAAgB,CAAC,CAAC;AACpE,CAAC;AAED,SAAS,sBAAsB,CAC3B,YAA8B,EAAE,eAAoC,EACpE,QAAiB;IACnB,OAAU,YAAY,CAAC,CAAC,CAAC,SAAI,YAAY,CAAC,CAAC,CAAC,SAAI,eAAe,SAAI,QAAU,CAAC;AAChF,CAAC"}