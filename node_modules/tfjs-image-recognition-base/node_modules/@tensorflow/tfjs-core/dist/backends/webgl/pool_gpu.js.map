{"version": 3, "file": "pool_gpu.js", "sourceRoot": "", "sources": ["../../../src/backends/webgl/pool_gpu.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;;GAeG;;AAMH;IAKE,uBACI,QAAoB,EAAE,QAAqB,EAAE,gBAAyB,EACtE,gBAAwB,EAAE,mBAA2B;QAArD,iCAAA,EAAA,wBAAwB;QAAE,oCAAA,EAAA,2BAA2B;QANzD,kBAAa,GAAG,CAAC,GAAG,CAAC,CAAC;QAOpB,IAAI,QAAQ,KAAK,KAAK,IAAI,gBAAgB,EAAE;YAC1C,MAAM,IAAI,KAAK,CAAC,4CAA4C,CAAC,CAAC;SAC/D;QAED,IAAM,WAAW,GAAG,QAAQ,CAAC,WAAW,CAAC;QACzC,IAAM,YAAY,GAAG,QAAQ,CAAC,YAAY,CAAC;QAC3C,IAAM,WAAW,GAAG,QAAQ,CAAC,WAAW,CAAC;QACzC,IAAM,cAAc,GAAG,QAAQ,CAAC,cAAc,CAAC;QAC/C,IAAM,aAAa,GAAG,QAAQ,CAAC,aAAa,CAAC;QAC7C,IAAM,qBAAqB,GAAG,QAAQ,CAAC,qBAAqB,CAAC;QAC7D,IAAM,oBAAoB,GAAG,QAAQ,CAAC,oBAAoB,CAAC;QAE3D,IAAM,MAAM,GAAG,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC;QACpC,IAAM,OAAO,GAAG,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC;QACtC,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,QAAQ,CAAC;QAErC,IAAM,SAAS,GAAG,QAAQ,KAAK,KAAK,CAAC;QACrC,IAAM,uBAAuB,GAAG,gBAAc,QAAQ,CAAC,QAAQ,iBAC3D,QAAQ,CAAC,OAAO,iBAAY,QAAQ,CAAC,UAAU,SAAM,CAAC;QAC1D,IAAM,kBAAkB,GACpB,WAAS,QAAQ,CAAC,OAAO,iBAAY,QAAQ,CAAC,UAAU,SAAM,CAAC;QAEnE,IAAI,mBAAmB,GAAG,KAAK,CAAC;QAChC,IAAI,CAAC,SAAS,EAAE;YACd,2DAA2D;YAC3D,mBAAmB,GAAG,cAAc,CAAC;SACtC;QAED,IAAI,gBAAgB,EAAE;YACpB,IAAM,WAAS,GAAG,IAAI,CAAC;YAEvB,IAAI,CAAC,QAAQ,GAAG,2CACgB,YAAY,UAAK,WAAW,6CAC/B,MAAM,UAAK,OAAO,yiBAkBnB,qBAAqB,+BACjC,cAAc,oFAGF,QAAQ,CAAC,QAAQ,yFAIf,oBAAoB,iCAChC,aAAa,wFAGD,QAAQ,CAAC,OAAO,wWAU1B,WAAS,8IAIzB,gBAAgB,CAAC,CAAC,CAAC,CAAC,mBAAmB,CAAC,CAAC,CAAC,uBAAuB,CAAC,CAAC;gBACzB,kBAAkB,CAAC,CAAC,CAAC;gBAC5C,UAAQ,oBAAoB,UAAO,oHAMzD,CAAC;YACF,OAAO;SACR;QAED,IAAM,SAAS,GAAG,KAAK,CAAC;QAExB,IAAI,WAAW,GAAM,QAAQ,SAAI,QAAQ,SAAI,QAAQ,MAAG;YACpD,mEAAmE,CAAC;QACxE,IAAI,QAAQ,KAAK,KAAK,EAAE;YACtB,WAAW,GAAG,kBAAkB,CAAC;SAClC;QAED,IAAM,sBAAsB,GAAG,IAAI,CAAC,KAAK,CAAC,WAAW,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;QAC/D,IAAM,wBAAwB,GAAG,WAAW,GAAG,CAAC,CAAC;QAEjD,IAAM,aAAa,GAAG,iBACd,SAAS,2FAGG,SAAS,0CAE5B,CAAC;QAEF,IAAI,CAAC,QAAQ,GAAG,yCACgB,YAAY,UAAK,WAAW,2CAC/B,MAAM,UAAK,OAAO,oDACT,mBAAmB,kLAM/B,QAAQ,CAAC,OAAO,+fAkBZ,mBAAmB,iGAIrB,qBAAqB,6BACjC,cAAc,gFAGF,QAAQ,CAAC,QAAQ,mFAIf,sBAAsB,2DACjB,aAAa,gIAIX,aAAa,0DACT,aAAa,0DACb,aAAa,4CAG5C,aAAa,uDAGK,sBAAsB,0BACtC,wBAAwB,KAAK,CAAC,mOAQhC,aAAa,gCACJ,wBAAwB,KAAK,CAAC,iIAGZ,aAAa,oHAKxC,aAAa,gCACJ,wBAAwB,KAAK,CAAC,iIAGZ,aAAa,0DACT,aAAa,gFAI5C,aAAa,oDAGP,WAAW,sBAE1B,CAAC;IACJ,CAAC;IACH,oBAAC;AAAD,CAAC,AA1MD,IA0MC;AA1MY,sCAAa;AA4M1B;IAKE,uBACI,QAAoB,EAAE,QAAqB,EAAE,gBAAyB,EACtE,gBAAwB,EAAE,mBAA2B;QAArD,iCAAA,EAAA,wBAAwB;QAAE,oCAAA,EAAA,2BAA2B;QANzD,kBAAa,GAAG,CAAC,GAAG,CAAC,CAAC;QAOpB,IAAI,QAAQ,KAAK,KAAK,IAAI,gBAAgB,EAAE;YAC1C,MAAM,IAAI,KAAK,CAAC,4CAA4C,CAAC,CAAC;SAC/D;QAED,IAAM,WAAW,GAAG,QAAQ,CAAC,WAAW,CAAC;QACzC,IAAM,WAAW,GAAG,QAAQ,CAAC,WAAW,CAAC;QACzC,IAAM,YAAY,GAAG,QAAQ,CAAC,YAAY,CAAC;QAC3C,IAAM,WAAW,GAAG,QAAQ,CAAC,WAAW,CAAC;QACzC,IAAM,aAAa,GAAG,QAAQ,CAAC,aAAa,CAAC;QAC7C,IAAM,cAAc,GAAG,QAAQ,CAAC,cAAc,CAAC;QAC/C,IAAM,aAAa,GAAG,QAAQ,CAAC,aAAa,CAAC;QAC7C,IAAM,oBAAoB,GAAG,QAAQ,CAAC,oBAAoB,CAAC;QAC3D,IAAM,qBAAqB,GAAG,QAAQ,CAAC,qBAAqB,CAAC;QAC7D,IAAM,oBAAoB,GAAG,QAAQ,CAAC,oBAAoB,CAAC;QAE3D,IAAM,QAAQ,GAAG,QAAQ,CAAC,OAAO,CAAC,KAAK,CAAC;QACxC,IAAM,MAAM,GAAG,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC;QACpC,IAAM,OAAO,GAAG,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC;QACtC,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,QAAQ,CAAC;QAErC,IAAM,SAAS,GAAG,QAAQ,KAAK,KAAK,CAAC;QAErC,IAAI,mBAAmB,GAAG,KAAK,CAAC;QAChC,IAAI,CAAC,SAAS,EAAE;YACd,2DAA2D;YAC3D,mBAAmB,GAAG,cAAc,CAAC;SACtC;QAED,IAAI,gBAAgB,EAAE;YACpB,IAAM,WAAS,GAAG,IAAI,CAAC;YAEvB,IAAI,CAAC,QAAQ,GAAG,wDAEF,WAAW,UAAK,YAAY,UAAK,WAAW,6CAC7B,QAAQ,UAAK,MAAM,UAAK,OAAO,ykBAkBhC,oBAAoB,+BAChC,aAAa,oFAGD,QAAQ,CAAC,OAAO,yFAId,qBAAqB,iCACjC,cAAc,wFAGF,QAAQ,CAAC,QAAQ,+FAIf,oBAAoB,mCAChC,aAAa,4FAGD,QAAQ,CAAC,OAAO,6XAU1B,WAAS,oJAI3B,gBAAgB,CAAC,CAAC;gBACd,CAAC,mBAAmB,CAAC,CAAC;oBACjB,gBAAc,QAAQ,CAAC,OAAO,iBAC1B,QAAQ,CAAC,QAAQ,iBAAY,QAAQ,CAAC,OAAO,iBAC7C,QAAQ,CAAC,UAAU,UAAO,CAAC,CAAC;oBAChC,YAAU,QAAQ,CAAC,QAAQ,iBACvB,QAAQ,CAAC,OAAO,iBAAY,QAAQ,CAAC,UAAU,UAAO,CAAC,CAAC,CAAC;gBAClE,UAAQ,qBAAqB,WAAM,oBAAoB,uCACxC,oBAAoB,UAAO,uIAOjD,CAAC;YACF,OAAO;SACR;QAED,IAAM,SAAS,GAAG,KAAK,CAAC;QAExB,IAAI,WAAW,GAAM,QAAQ,SAAI,QAAQ,SAAI,QAAQ,MAAG;YACpD,mEAAmE,CAAC;QACxE,IAAI,QAAQ,KAAK,KAAK,EAAE;YACtB,WAAW,GAAG,kBAAkB,CAAC;SAClC;QAED,IAAM,sBAAsB,GAAG,IAAI,CAAC,KAAK,CAAC,WAAW,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;QAC/D,IAAM,wBAAwB,GAAG,WAAW,GAAG,CAAC,CAAC;QAEjD,IAAM,aAAa,GAAG,iBACd,SAAS,2FAGG,SAAS,0CAE5B,CAAC;QAEF,IAAI,CAAC,QAAQ,GAAG,kDAEJ,WAAW,UAAK,YAAY,UAAK,WAAW,2CAC3B,QAAQ,UAAK,MAAM,UAAK,OAAO,oDACtB,mBAAmB,2LAM/B,QAAQ,CAAC,OAAO,kkBAmBZ,mBAAmB,iGAIrB,oBAAoB,6BAChC,aAAa,gFAGD,QAAQ,CAAC,OAAO,mFAId,qBAAqB,6BACnC,cAAc,oFAGA,QAAQ,CAAC,QAAQ,yFAIf,sBAAsB,6DACjB,aAAa,+IAIP,aAAa,iEACT,aAAa,iEACb,aAAa,iDAGhD,aAAa,2DAGK,sBAAsB,4BACtC,wBAAwB,KAAK,CAAC,sPAQhC,aAAa,kCACJ,wBAAwB,KAAK,CAAC,gJAGR,aAAa,6HAK5C,aAAa,kCACJ,wBAAwB,KAAK,CAAC,gJAGR,aAAa,iEACT,aAAa,uFAIhD,aAAa,0DAGP,WAAW,iCAG5B,CAAC;IACJ,CAAC;IACH,oBAAC;AAAD,CAAC,AArOD,IAqOC;AArOY,sCAAa"}