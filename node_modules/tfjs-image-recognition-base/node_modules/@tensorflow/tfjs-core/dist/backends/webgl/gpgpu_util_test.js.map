{"version": 3, "file": "gpgpu_util_test.js", "sourceRoot": "", "sources": ["../../../src/backends/webgl/gpgpu_util_test.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;;GAeG;;AAEH,mDAAqD;AACrD,6EAAyD;AACzD,iDAA6C;AAC7C,yCAA2C;AAC3C,qCAAuC;AAEvC,gCAAiB,CAAC,+BAA+B,EAAE,wCAAU,EAAE;IAC7D,IAAI,KAAmB,CAAC;IAExB,UAAU,CAAC;QACT,KAAK,GAAG,IAAI,4BAAY,EAAE,CAAC;IAC7B,CAAC,CAAC,CAAC;IAEH,SAAS,CAAC;QACR,KAAK,CAAC,OAAO,EAAE,CAAC;IAClB,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,sCAAsC,EAAE;QACzC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,YAAY,CAAC,KAAK,CAAC,EAAE,CAAC,UAAU,CAAC,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;QAClE,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,YAAY,CAAC,KAAK,CAAC,EAAE,CAAC,YAAY,CAAC,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;IACtE,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,gBAAgB,EAAE;QACnB,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,YAAY,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;IAC/D,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,sCAAsC,EAAE;QACzC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,YAAY,CAAC,KAAK,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;QAC9D,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,YAAY,CAAC,KAAK,CAAC,EAAE,CAAC,mBAAmB,CAAC,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;IAC7E,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,6BAA6B,EAAE;QAChC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,YAAY,CAAC,KAAK,CAAC,EAAE,CAAC,SAAS,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;QAChE,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,YAAY,CAAC,KAAK,CAAC,EAAE,CAAC,cAAc,CAAC,CAAC;aACjD,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC;IAC9B,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,sBAAsB,EAAE;QACzB,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,YAAY,CAAC,KAAK,CAAC,EAAE,CAAC,YAAY,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;IACrE,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC;AAEH,gCAAiB,CAAC,uCAAuC,EAAE,wCAAU,EAAE;IACrE,EAAE,CAAC,uDAAuD,EAAE;QAC1D,IAAM,KAAK,GAAG,IAAI,4BAAY,EAAE,CAAC;QACjC,IAAM,aAAa,GAAG,QAAQ,CAAC,gBAAgB,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;QAC1D,IAAM,KAAK,GAAG,KAAK,CAAC;QACpB,IAAM,GAAG,GAAG,UAAU,CAAC,0BAA0B,CAC7C,KAAK,CAAC,EAAE,EAAE,KAAK,EAAE,EAAE,EAAE,EAAE,EAAE,aAAa,CAAC,CAAC;QAC5C,KAAK,CAAC,EAAE,CAAC,WAAW,CAAC,KAAK,CAAC,EAAE,CAAC,UAAU,EAAE,GAAG,CAAC,CAAC;QAC/C,MAAM,CACF,KAAK,CAAC,EAAE,CAAC,eAAe,CAAC,KAAK,CAAC,EAAE,CAAC,UAAU,EAAE,KAAK,CAAC,EAAE,CAAC,cAAc,CAAC,CAAC;aACtE,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC,aAAa,CAAC,CAAC;QACrC,MAAM,CACF,KAAK,CAAC,EAAE,CAAC,eAAe,CAAC,KAAK,CAAC,EAAE,CAAC,UAAU,EAAE,KAAK,CAAC,EAAE,CAAC,cAAc,CAAC,CAAC;aACtE,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC,aAAa,CAAC,CAAC;QACrC,KAAK,CAAC,EAAE,CAAC,WAAW,CAAC,KAAK,CAAC,EAAE,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC;QAChD,KAAK,CAAC,mBAAmB,CAAC,GAAG,CAAC,CAAC;QAC/B,KAAK,CAAC,OAAO,EAAE,CAAC;IAClB,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,yDAAyD,EAAE;QAC5D,IAAM,KAAK,GAAG,IAAI,4BAAY,EAAE,CAAC;QACjC,IAAM,aAAa,GAAG,QAAQ,CAAC,gBAAgB,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;QAC1D,IAAM,KAAK,GAAG,KAAK,CAAC;QACpB,IAAM,GAAG,GAAG,UAAU,CAAC,0BAA0B,CAC7C,KAAK,CAAC,EAAE,EAAE,KAAK,EAAE,EAAE,EAAE,EAAE,EAAE,aAAa,CAAC,CAAC;QAC5C,KAAK,CAAC,EAAE,CAAC,WAAW,CAAC,KAAK,CAAC,EAAE,CAAC,UAAU,EAAE,GAAG,CAAC,CAAC;QAC/C,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,eAAe,CACpB,KAAK,CAAC,EAAE,CAAC,UAAU,EAAE,KAAK,CAAC,EAAE,CAAC,kBAAkB,CAAC,CAAC;aACxD,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC,OAAO,CAAC,CAAC;QAC/B,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,eAAe,CACpB,KAAK,CAAC,EAAE,CAAC,UAAU,EAAE,KAAK,CAAC,EAAE,CAAC,kBAAkB,CAAC,CAAC;aACxD,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC,OAAO,CAAC,CAAC;QAC/B,KAAK,CAAC,EAAE,CAAC,WAAW,CAAC,KAAK,CAAC,EAAE,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC;QAChD,KAAK,CAAC,mBAAmB,CAAC,GAAG,CAAC,CAAC;QAC/B,KAAK,CAAC,OAAO,EAAE,CAAC;IAClB,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC;AAEH,gCAAiB,CAAC,sCAAsC,EAAE,wCAAU,EAAE;IACpE,EAAE,CAAC,uDAAuD,EAAE;QAC1D,IAAM,KAAK,GAAG,IAAI,4BAAY,EAAE,CAAC;QACjC,IAAM,aAAa,GAAG,QAAQ,CAAC,gBAAgB,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;QAC1D,IAAM,KAAK,GAAG,KAAK,CAAC;QACpB,IAAM,GAAG,GAAG,UAAU,CAAC,yBAAyB,CAC5C,KAAK,CAAC,EAAE,EAAE,KAAK,EAAE,EAAE,EAAE,EAAE,EAAE,aAAa,CAAC,CAAC;QAC5C,KAAK,CAAC,EAAE,CAAC,WAAW,CAAC,KAAK,CAAC,EAAE,CAAC,UAAU,EAAE,GAAG,CAAC,CAAC;QAC/C,MAAM,CACF,KAAK,CAAC,EAAE,CAAC,eAAe,CAAC,KAAK,CAAC,EAAE,CAAC,UAAU,EAAE,KAAK,CAAC,EAAE,CAAC,cAAc,CAAC,CAAC;aACtE,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC,aAAa,CAAC,CAAC;QACrC,MAAM,CACF,KAAK,CAAC,EAAE,CAAC,eAAe,CAAC,KAAK,CAAC,EAAE,CAAC,UAAU,EAAE,KAAK,CAAC,EAAE,CAAC,cAAc,CAAC,CAAC;aACtE,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC,aAAa,CAAC,CAAC;QACrC,KAAK,CAAC,EAAE,CAAC,WAAW,CAAC,KAAK,CAAC,EAAE,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC;QAChD,KAAK,CAAC,mBAAmB,CAAC,GAAG,CAAC,CAAC;QAC/B,KAAK,CAAC,OAAO,EAAE,CAAC;IAClB,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,yDAAyD,EAAE;QAC5D,IAAM,KAAK,GAAG,IAAI,4BAAY,EAAE,CAAC;QACjC,IAAM,aAAa,GAAG,QAAQ,CAAC,gBAAgB,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;QAC1D,IAAM,KAAK,GAAG,KAAK,CAAC;QACpB,IAAM,GAAG,GAAG,UAAU,CAAC,yBAAyB,CAC5C,KAAK,CAAC,EAAE,EAAE,KAAK,EAAE,EAAE,EAAE,EAAE,EAAE,aAAa,CAAC,CAAC;QAC5C,KAAK,CAAC,EAAE,CAAC,WAAW,CAAC,KAAK,CAAC,EAAE,CAAC,UAAU,EAAE,GAAG,CAAC,CAAC;QAC/C,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,eAAe,CACpB,KAAK,CAAC,EAAE,CAAC,UAAU,EAAE,KAAK,CAAC,EAAE,CAAC,kBAAkB,CAAC,CAAC;aACxD,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC,OAAO,CAAC,CAAC;QAC/B,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,eAAe,CACpB,KAAK,CAAC,EAAE,CAAC,UAAU,EAAE,KAAK,CAAC,EAAE,CAAC,kBAAkB,CAAC,CAAC;aACxD,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC,OAAO,CAAC,CAAC;QAC/B,KAAK,CAAC,EAAE,CAAC,WAAW,CAAC,KAAK,CAAC,EAAE,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC;QAChD,KAAK,CAAC,mBAAmB,CAAC,GAAG,CAAC,CAAC;QAC/B,KAAK,CAAC,OAAO,EAAE,CAAC;IAClB,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC"}