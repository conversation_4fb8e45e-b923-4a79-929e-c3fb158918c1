{"version": 3, "file": "gpgpu_util.js", "sourceRoot": "", "sources": ["../../../src/backends/webgl/gpgpu_util.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;;GAeG;;AAIH,+CAAkD;AAClD,qCAAuC;AAEvC,yCAA2C;AAE3C,SAAgB,kBAAkB,CAC9B,EAAyB,EAAE,KAAc;IAC3C,IAAM,IAAI,GAAG,iCAAkB,EAAE,CAAC;IAClC,IAAM,kBAAkB,GAAM,IAAI,CAAC,OAAO,0CAEtC,IAAI,CAAC,SAAS,iCACd,IAAI,CAAC,SAAS,uBACd,IAAI,CAAC,SAAS,kHAKd,CAAC;IACL,OAAO,UAAU,CAAC,kBAAkB,CAAC,EAAE,EAAE,KAAK,EAAE,kBAAkB,CAAC,CAAC;AACtE,CAAC;AAdD,gDAcC;AAED,SAAgB,kBAAkB,CAC9B,EAAyB,EAAE,KAAc;IAC3C,mEAAmE;IACnE,IAAM,WAAW,GAAG,IAAI,YAAY,CAChC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACtE,OAAO,UAAU,CAAC,wBAAwB,CAAC,EAAE,EAAE,KAAK,EAAE,WAAW,CAAC,CAAC;AACrE,CAAC;AAND,gDAMC;AAED,SAAgB,iBAAiB,CAC7B,EAAyB,EAAE,KAAc;IAC3C,iDAAiD;IACjD,IAAM,qBAAqB,GAAG,IAAI,WAAW,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IAClE,OAAO,UAAU,CAAC,uBAAuB,CAAC,EAAE,EAAE,KAAK,EAAE,qBAAqB,CAAC,CAAC;AAC9E,CAAC;AALD,8CAKC;AAED,SAAS,yBAAyB,CAC9B,EAAyB,EAAE,KAAc,EAAE,KAAa,EAAE,MAAc,EACxE,cAAsB,EAAE,aAAqB,EAC7C,WAAmB;IACrB,UAAU,CAAC,mBAAmB,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;IAC9C,IAAM,OAAO,GAAG,UAAU,CAAC,aAAa,CAAC,EAAE,EAAE,KAAK,CAAC,CAAC;IAEpD,IAAM,KAAK,GAAG,EAAE,CAAC,UAAU,CAAC;IAC5B,UAAU,CAAC,YAAY,CAAC,EAAE,EAAE,KAAK,EAAE,cAAM,OAAA,EAAE,CAAC,WAAW,CAAC,KAAK,EAAE,OAAO,CAAC,EAA9B,CAA8B,CAAC,CAAC;IACzE,UAAU,CAAC,YAAY,CACnB,EAAE,EAAE,KAAK,EACT,cAAM,OAAA,EAAE,CAAC,aAAa,CAAC,KAAK,EAAE,EAAE,CAAC,cAAc,EAAE,EAAE,CAAC,aAAa,CAAC,EAA5D,CAA4D,CAAC,CAAC;IACxE,UAAU,CAAC,YAAY,CACnB,EAAE,EAAE,KAAK,EACT,cAAM,OAAA,EAAE,CAAC,aAAa,CAAC,KAAK,EAAE,EAAE,CAAC,cAAc,EAAE,EAAE,CAAC,aAAa,CAAC,EAA5D,CAA4D,CAAC,CAAC;IACxE,UAAU,CAAC,YAAY,CACnB,EAAE,EAAE,KAAK,EACT,cAAM,OAAA,EAAE,CAAC,aAAa,CAAC,KAAK,EAAE,EAAE,CAAC,kBAAkB,EAAE,EAAE,CAAC,OAAO,CAAC,EAA1D,CAA0D,CAAC,CAAC;IACtE,UAAU,CAAC,YAAY,CACnB,EAAE,EAAE,KAAK,EACT,cAAM,OAAA,EAAE,CAAC,aAAa,CAAC,KAAK,EAAE,EAAE,CAAC,kBAAkB,EAAE,EAAE,CAAC,OAAO,CAAC,EAA1D,CAA0D,CAAC,CAAC;IACtE,UAAU,CAAC,YAAY,CACnB,EAAE,EAAE,KAAK,EACT,cAAM,OAAA,EAAE,CAAC,UAAU,CACf,KAAK,EAAE,CAAC,EAAE,cAAc,EAAE,KAAK,EAAE,MAAM,EAAE,CAAC,EAAE,aAAa,EACzD,WAAW,EAAE,IAAI,CAAC,EAFhB,CAEgB,CAAC,CAAC;IAC5B,UAAU,CAAC,YAAY,CAAC,EAAE,EAAE,KAAK,EAAE,cAAM,OAAA,EAAE,CAAC,WAAW,CAAC,EAAE,CAAC,UAAU,EAAE,IAAI,CAAC,EAAnC,CAAmC,CAAC,CAAC;IAC9E,OAAO,OAAO,CAAC;AACjB,CAAC;AAED,SAAgB,0BAA0B,CACtC,EAAyB,EAAE,KAAc,EAAE,IAAY,EAAE,OAAe,EACxE,aAA4B;IACxB,IAAA,qEAC8D,EAD7D,aAAK,EAAE,cACsD,CAAC;IACrE,OAAO,yBAAyB,CAC5B,EAAE,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,aAAa,CAAC,mBAAmB,EAC3D,aAAa,CAAC,kBAAkB,EAAE,EAAE,CAAC,KAAK,CAAC,CAAC;AAClD,CAAC;AARD,gEAQC;AAED,SAAgB,0BAA0B,CACtC,EAAyB,EAAE,KAAc,EAAE,IAAY,EAAE,OAAe,EACxE,aAA4B;IACxB,IAAA,qEAC8D,EAD7D,aAAK,EAAE,cACsD,CAAC;IACrE,OAAO,yBAAyB,CAC5B,EAAE,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,aAAa,CAAC,uBAAuB,EAC/D,aAAa,CAAC,kBAAkB,EAAE,aAAa,CAAC,oBAAoB,CAAC,CAAC;AAC5E,CAAC;AARD,gEAQC;AAED,SAAgB,gCAAgC,CAC5C,EAAyB,EAAE,KAAc,EAAE,IAAY,EAAE,OAAe,EACxE,aAA4B;IACxB,IAAA,qEAC8D,EAD7D,aAAK,EAAE,cACsD,CAAC;IACrE,OAAO,yBAAyB,CAC5B,EAAE,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,EAAE,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,EAAE,EAAE,CAAC,aAAa,CAAC,CAAC;AACpE,CAAC;AAPD,4EAOC;AAED,SAAgB,yBAAyB,CACrC,EAAyB,EAAE,KAAc,EAAE,IAAY,EAAE,OAAe,EACxE,aAA4B;IACxB,IAAA,mEAC4D,EAD3D,aAAK,EAAE,cACoD,CAAC;IACnE,OAAO,yBAAyB,CAC5B,EAAE,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,aAAa,CAAC,yBAAyB,EACjE,EAAE,CAAC,IAAI,EAAE,EAAE,CAAC,KAAK,CAAC,CAAC;AACzB,CAAC;AARD,8DAQC;AAED,SAAgB,gCAAgC,CAC5C,EAAyB,EAAE,KAAc,EAAE,IAAY,EAAE,OAAe,EACxE,aAA4B;IACxB,IAAA,mEAC4D,EAD3D,aAAK,EAAE,cACoD,CAAC;IACnE,OAAO,yBAAyB,CAC5B,EAAE,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,aAAa,CAAC,6BAA6B,EACrE,EAAE,CAAC,IAAI,EAAE,aAAa,CAAC,oBAAoB,CAAC,CAAC;AACnD,CAAC;AARD,4EAQC;AAED,SAAgB,iCAAiC,CAC7C,EAAyB,EAAE,KAAc,EAAE,OAAqB,EAChE,YAAyB;IAC3B,IAAM,SAAS,GAAG,CAAC,CAAC,CAAe,gCAAgC;IACnE,IAAM,QAAQ,GAAG,CAAC,GAAG,CAAC,CAAC,CAAY,yBAAyB;IAC5D,IAAM,MAAM,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAE,wCAAwC;IAC3E,UAAU,CAAC,YAAY,CACnB,EAAE,EAAE,KAAK,EAAE,cAAM,OAAA,EAAE,CAAC,UAAU,CAAC,EAAE,CAAC,YAAY,EAAE,YAAY,CAAC,EAA5C,CAA4C,CAAC,CAAC;IACnE,IAAM,OAAO,GAAG,UAAU,CAAC,kCAAkC,CACzD,EAAE,EAAE,KAAK,EAAE,OAAO,EAAE,cAAc,EAAE,YAAY,EAAE,CAAC,EAAE,MAAM,EAAE,SAAS,CAAC,CAAC;IAC5E,OAAO,OAAO;QACV,UAAU,CAAC,kCAAkC,CACzC,EAAE,EAAE,KAAK,EAAE,OAAO,EAAE,IAAI,EAAE,YAAY,EAAE,CAAC,EAAE,MAAM,EAAE,QAAQ,CAAC,CAAC;AACvE,CAAC;AAbD,8EAaC;AAED,SAAgB,0BAA0B,CACtC,EAAyB,EAAE,KAAc,EAAE,OAAqB,EAChE,KAAa,EAAE,MAAc,EAAE,IAAgB,EAC/C,aAA4B;IAC9B,UAAU,CAAC,YAAY,CACnB,EAAE,EAAE,KAAK,EAAE,cAAM,OAAA,EAAE,CAAC,WAAW,CAAC,EAAE,CAAC,UAAU,EAAE,OAAO,CAAC,EAAtC,CAAsC,CAAC,CAAC;IAE7D,IAAI,aAAyB,EAAE,aAAqB,EAAE,cAAsB,CAAC;IAC7E,IAAI,IAAI,YAAY,UAAU,EAAE;QAC9B,aAAa,GAAG,IAAI,UAAU,CAAC,KAAK,GAAG,MAAM,GAAG,CAAC,CAAC,CAAC;QACnD,aAAa,GAAG,EAAE,CAAC,aAAa,CAAC;QACjC,cAAc,GAAG,EAAE,CAAC,IAAI,CAAC;KAC1B;SAAM;QACL,aAAa,GAAG,IAAI,YAAY,CAAC,KAAK,GAAG,MAAM,GAAG,CAAC,CAAC,CAAC;QACrD,aAAa,GAAG,EAAE,CAAC,KAAK,CAAC;QACzB,cAAc,GAAG,aAAa,CAAC,yBAAyB,CAAC;KAC1D;IAED,aAAa,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;IAExB,UAAU,CAAC,YAAY,CACnB,EAAE,EAAE,KAAK,EACT,cAAM,OAAA,EAAE,CAAC,UAAU,CACf,EAAE,CAAC,UAAU,EAAE,CAAC,EAAE,cAAc,EAAE,KAAK,EAAE,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC,IAAI,EAC3D,aAAa,EAAE,aAAa,CAAC,EAF3B,CAE2B,CAAC,CAAC;IAEvC,UAAU,CAAC,YAAY,CAAC,EAAE,EAAE,KAAK,EAAE,cAAM,OAAA,EAAE,CAAC,WAAW,CAAC,EAAE,CAAC,UAAU,EAAE,IAAI,CAAC,EAAnC,CAAmC,CAAC,CAAC;AAChF,CAAC;AA3BD,gEA2BC;AAED,SAAgB,wBAAwB,CACpC,EAAyB,EAAE,KAAc,EAAE,OAAqB,EAChE,MACgB;IAClB,UAAU,CAAC,YAAY,CACnB,EAAE,EAAE,KAAK,EAAE,cAAM,OAAA,EAAE,CAAC,WAAW,CAAC,EAAE,CAAC,UAAU,EAAE,OAAO,CAAC,EAAtC,CAAsC,CAAC,CAAC;IAC7D,IAAK,MAAoB,CAAC,IAAI,YAAY,UAAU,EAAE;QACpD,UAAU,CAAC,YAAY,CACnB,EAAE,EAAE,KAAK,EACT,cAAM,OAAA,EAAE,CAAC,UAAU,CACf,EAAE,CAAC,UAAU,EAAE,CAAC,EAAE,EAAE,CAAC,IAAI,EAAE,MAAM,CAAC,KAAK,EAAE,MAAM,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC,IAAI,EAClE,EAAE,CAAC,aAAa,EAAG,MAAoB,CAAC,IAAI,CAAC,EAF3C,CAE2C,CAAC,CAAC;KACxD;SAAM;QACL,UAAU,CAAC,YAAY,CACnB,EAAE,EAAE,KAAK,EACT,cAAM,OAAA,EAAE,CAAC,UAAU,CACf,EAAE,CAAC,UAAU,EAAE,CAAC,EAAE,EAAE,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,EAAE,EAAE,CAAC,aAAa,EACpD,MACoB,CAAC,EAHnB,CAGmB,CAAC,CAAC;KAChC;IAED,UAAU,CAAC,YAAY,CAAC,EAAE,EAAE,KAAK,EAAE,cAAM,OAAA,EAAE,CAAC,WAAW,CAAC,EAAE,CAAC,UAAU,EAAE,IAAI,CAAC,EAAnC,CAAmC,CAAC,CAAC;AAChF,CAAC;AAtBD,4DAsBC;AAED,SAAgB,6BAA6B,CACzC,GAA2B,EAAE,KAAc,EAAE,IAAY,EAAE,OAAe,EAC1E,aAA4B;IAC9B,8BAA8B;IAC9B,IAAM,MAAM,GAAG,GAAG,CAAC,YAAY,EAAE,CAAC;IAClC,UAAU,CAAC,YAAY,CACnB,GAAG,EAAE,KAAK,EAAE,cAAM,OAAA,GAAG,CAAC,UAAU,CAAC,GAAG,CAAC,iBAAiB,EAAE,MAAM,CAAC,EAA7C,CAA6C,CAAC,CAAC;IAErE,6DAA6D;IAC7D,IAAM,aAAa,GAAG,CAAC,CAAC;IACxB,IAAM,cAAc,GAAG,CAAC,CAAC;IACzB,IAAM,eAAe,GAAG,aAAa,GAAG,cAAc,GAAG,IAAI,GAAG,OAAO,CAAC;IAExE,UAAU,CAAC,YAAY,CACnB,GAAG,EAAE,KAAK,EACV,cAAM,OAAA,GAAG,CAAC,UAAU,CAChB,GAAG,CAAC,iBAAiB,EAAE,eAAe,EAAE,GAAG,CAAC,WAAW,CAAC,EADtD,CACsD,CAAC,CAAC;IAElE,yEAAyE;IACzE,UAAU;IACV,UAAU,CAAC,YAAY,CACnB,GAAG,EAAE,KAAK,EACV,cAAM,OAAA,GAAG,CAAC,UAAU,CAAC,CAAC,EAAE,CAAC,EAAE,OAAO,EAAE,IAAI,EAAE,GAAG,CAAC,IAAI,EAAE,GAAG,CAAC,KAAK,EAAE,CAAC,CAAC,EAA3D,CAA2D,CAAC,CAAC;IAEvE,UAAU,CAAC,YAAY,CACnB,GAAG,EAAE,KAAK,EAAE,cAAM,OAAA,GAAG,CAAC,UAAU,CAAC,GAAG,CAAC,iBAAiB,EAAE,IAAI,CAAC,EAA3C,CAA2C,CAAC,CAAC;IAEnE,OAAO,MAAM,CAAC;AAChB,CAAC;AA5BD,sEA4BC;AAED,SAAgB,+BAA+B,CAC3C,EAAyB,EAAE,MAAmB,EAC9C,IAAY;IACd,IAAM,GAAG,GAAG,EAA4B,CAAC;IAEzC,IAAM,cAAc,GAAG,IAAI,YAAY,CAAC,IAAI,CAAC,CAAC;IAE9C,GAAG,CAAC,UAAU,CAAC,GAAG,CAAC,iBAAiB,EAAE,MAAM,CAAC,CAAC;IAC9C,GAAG,CAAC,gBAAgB,CAAC,GAAG,CAAC,iBAAiB,EAAE,CAAC,EAAE,cAAc,CAAC,CAAC;IAC/D,GAAG,CAAC,UAAU,CAAC,GAAG,CAAC,iBAAiB,EAAE,IAAI,CAAC,CAAC;IAE5C,OAAO,cAAc,CAAC;AACxB,CAAC;AAZD,0EAYC;AAED,SAAgB,+CAA+C,CAC3D,EAAyB,EAAE,KAAc,EAAE,IAAY,EAAE,OAAe,EACxE,aAA4B;IACxB,IAAA,qEAC8D,EAD7D,SAAC,EAAE,SAC0D,CAAC;IAErE,IAAM,WAAW,GAAG,CAAC,CAAC;IACtB,IAAM,cAAc,GAAG,IAAI,UAAU,CACjC,QAAQ,CAAC,kCAAkC,CAAC,IAAI,GAAG,OAAO,EAAE,WAAW,CAAC,CAAC,CAAC;IAE9E,UAAU,CAAC,YAAY,CACnB,EAAE,EAAE,KAAK,EACT,cAAM,OAAA,EAAE,CAAC,UAAU,CACf,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,aAAa,CAAC,qBAAqB,EAAE,EAAE,CAAC,aAAa,EACjE,cAAc,CAAC,EAFb,CAEa,CAAC,CAAC;IAEzB,2EAA2E;IAC3E,uDAAuD;IACvD,OAAO,IAAI,YAAY,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;AACjD,CAAC;AAnBD,0GAmBC;AAED,SAAgB,8BAA8B,CAC1C,EAAyB,EAAE,MAAmB,EAAE,KAAa,EAAE,IAAY,EAC3E,IAAY,EAAE,YAAoB,EAAE,YAAoB,EACxD,aAA4B;IAC9B,IAAM,GAAG,GAAG,EAA4B,CAAC;IAEzC,IAAM,cAAc,GAChB,IAAI,YAAY,CAAC,QAAQ,CAAC,qCAAqC,CAC3D,YAAY,EAAE,YAAY,CAAC,CAAC,CAAC;IAErC,GAAG,CAAC,UAAU,CAAC,GAAG,CAAC,iBAAiB,EAAE,MAAM,CAAC,CAAC;IAC9C,GAAG,CAAC,gBAAgB,CAAC,GAAG,CAAC,iBAAiB,EAAE,CAAC,EAAE,cAAc,CAAC,CAAC;IAC/D,GAAG,CAAC,UAAU,CAAC,GAAG,CAAC,iBAAiB,EAAE,IAAI,CAAC,CAAC;IAE5C,OAAO,cAAc,CAAC;AACxB,CAAC;AAfD,wEAeC;AAED,SAAgB,qCAAqC,CACjD,EAAyB,EAAE,KAAc,EAAE,YAAoB,EAC/D,YAAoB;IACtB,IAAM,UAAU,GAAG,IAAI,YAAY,CAAC,YAAY,GAAG,YAAY,GAAG,CAAC,CAAC,CAAC;IACrE,UAAU,CAAC,YAAY,CACnB,EAAE,EAAE,KAAK,EACT,cAAM,OAAA,EAAE,CAAC,UAAU,CACf,CAAC,EAAE,CAAC,EAAE,YAAY,EAAE,YAAY,EAAE,EAAE,CAAC,IAAI,EAAE,EAAE,CAAC,KAAK,EAAE,UAAU,CAAC,EAD9D,CAC8D,CAAC,CAAC;IAE1E,OAAO,UAAU,CAAC;AACpB,CAAC;AAVD,sFAUC"}