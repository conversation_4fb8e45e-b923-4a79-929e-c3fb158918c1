{"version": 3, "file": "webgl_util.js", "sourceRoot": "", "sources": ["../../../src/backends/webgl/webgl_util.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;;GAeG;;AAEH,iDAAsC;AAEtC,iCAAmC;AAEnC,6CAA8C;AAC9C,uCAA4C;AAE5C,SAAgB,YAAY,CACxB,EAAyB,EAAE,SAAkB,EAAE,IAAa;IAC9D,IAAM,WAAW,GAAG,IAAI,EAAE,CAAC;IAC3B,IAAI,SAAS,EAAE;QACb,eAAe,CAAC,EAAE,CAAC,CAAC;KACrB;IACD,OAAO,WAAW,CAAC;AACrB,CAAC;AAPD,oCAOC;AAED,SAAS,eAAe,CAAC,EAAyB;IAChD,IAAM,KAAK,GAAG,EAAE,CAAC,QAAQ,EAAE,CAAC;IAC5B,IAAI,KAAK,KAAK,EAAE,CAAC,QAAQ,EAAE;QACzB,MAAM,IAAI,KAAK,CAAC,eAAe,GAAG,oBAAoB,CAAC,EAAE,EAAE,KAAK,CAAC,CAAC,CAAC;KACpE;AACH,CAAC;AAED,qEAAqE;AACrE,IAAM,WAAW,GAAG,OAAO,CAAC;AAC5B,IAAM,WAAW,GAAG,KAAK,CAAC;AAE1B,SAAgB,gBAAgB,CAAC,GAAW;IAC1C,IAAI,iBAAG,EAAE,CAAC,OAAO,CAAC,8BAA8B,CAAC,IAAI,GAAG,KAAK,CAAC;QAC1D,CAAC,WAAW,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,WAAW,CAAC,EAAE;QAChE,OAAO,IAAI,CAAC;KACb;IACD,OAAO,KAAK,CAAC;AACf,CAAC;AAND,4CAMC;AAED,SAAgB,oBAAoB,CAChC,EAAyB,EAAE,MAAc;IAC3C,QAAQ,MAAM,EAAE;QACd,KAAK,EAAE,CAAC,QAAQ;YACd,OAAO,UAAU,CAAC;QACpB,KAAK,EAAE,CAAC,YAAY;YAClB,OAAO,cAAc,CAAC;QACxB,KAAK,EAAE,CAAC,aAAa;YACnB,OAAO,eAAe,CAAC;QACzB,KAAK,EAAE,CAAC,iBAAiB;YACvB,OAAO,mBAAmB,CAAC;QAC7B,KAAK,EAAE,CAAC,6BAA6B;YACnC,OAAO,+BAA+B,CAAC;QACzC,KAAK,EAAE,CAAC,aAAa;YACnB,OAAO,eAAe,CAAC;QACzB,KAAK,EAAE,CAAC,kBAAkB;YACxB,OAAO,oBAAoB,CAAC;QAC9B;YACE,OAAO,wBAAsB,MAAQ,CAAC;KACzC;AACH,CAAC;AApBD,oDAoBC;AAED,SAAgB,mBAAmB,CAC/B,EAAyB,EAAE,KAAc,EAAE,aAAqB;IAClE,OAAO,WAAW,CACd,EAAE,EAAE,KAAK,EAAE,cAAM,OAAA,EAAE,CAAC,YAAY,CAAC,aAAa,CAAC,EAA9B,CAA8B,EAC/C,aAAa,GAAG,aAAa,GAAG,kCAAkC,CAAC,CAAC;AAC1E,CAAC;AALD,kDAKC;AAED,SAAgB,kBAAkB,CAC9B,EAAyB,EAAE,KAAc,EACzC,kBAA0B;IAC5B,IAAM,YAAY,GAAgB,WAAW,CACzC,EAAE,EAAE,KAAK,EAAE,cAAM,OAAA,EAAE,CAAC,YAAY,CAAC,EAAE,CAAC,aAAa,CAAC,EAAjC,CAAiC,EAClD,sCAAsC,CAAC,CAAC;IAC5C,YAAY,CACR,EAAE,EAAE,KAAK,EAAE,cAAM,OAAA,EAAE,CAAC,YAAY,CAAC,YAAY,EAAE,kBAAkB,CAAC,EAAjD,CAAiD,CAAC,CAAC;IACxE,YAAY,CAAC,EAAE,EAAE,KAAK,EAAE,cAAM,OAAA,EAAE,CAAC,aAAa,CAAC,YAAY,CAAC,EAA9B,CAA8B,CAAC,CAAC;IAC9D,IAAI,EAAE,CAAC,kBAAkB,CAAC,YAAY,EAAE,EAAE,CAAC,cAAc,CAAC,KAAK,KAAK,EAAE;QACpE,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,gBAAgB,CAAC,YAAY,CAAC,CAAC,CAAC;QAC/C,MAAM,IAAI,KAAK,CAAC,kCAAkC,CAAC,CAAC;KACrD;IACD,OAAO,YAAY,CAAC;AACtB,CAAC;AAdD,gDAcC;AAED,SAAgB,oBAAoB,CAChC,EAAyB,EAAE,KAAc,EACzC,oBAA4B;IAC9B,IAAM,cAAc,GAAgB,WAAW,CAC3C,EAAE,EAAE,KAAK,EAAE,cAAM,OAAA,EAAE,CAAC,YAAY,CAAC,EAAE,CAAC,eAAe,CAAC,EAAnC,CAAmC,EACpD,wCAAwC,CAAC,CAAC;IAC9C,YAAY,CACR,EAAE,EAAE,KAAK,EAAE,cAAM,OAAA,EAAE,CAAC,YAAY,CAAC,cAAc,EAAE,oBAAoB,CAAC,EAArD,CAAqD,CAAC,CAAC;IAC5E,YAAY,CAAC,EAAE,EAAE,KAAK,EAAE,cAAM,OAAA,EAAE,CAAC,aAAa,CAAC,cAAc,CAAC,EAAhC,CAAgC,CAAC,CAAC;IAChE,IAAI,EAAE,CAAC,kBAAkB,CAAC,cAAc,EAAE,EAAE,CAAC,cAAc,CAAC,KAAK,KAAK,EAAE;QACtE,yBAAyB,CACrB,oBAAoB,EAAE,EAAE,CAAC,gBAAgB,CAAC,cAAc,CAAC,CAAC,CAAC;QAC/D,MAAM,IAAI,KAAK,CAAC,oCAAoC,CAAC,CAAC;KACvD;IACD,OAAO,cAAc,CAAC;AACxB,CAAC;AAfD,oDAeC;AAED,IAAM,eAAe,GAAG,0BAA0B,CAAC;AACnD,SAAS,yBAAyB,CAC9B,YAAoB,EAAE,aAAqB;IAC7C,IAAM,qBAAqB,GAAG,eAAe,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;IAClE,IAAI,qBAAqB,IAAI,IAAI,EAAE;QACjC,OAAO,CAAC,GAAG,CAAC,0CAAwC,aAAe,CAAC,CAAC;QACrE,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC;QAC1B,OAAO;KACR;IAED,IAAM,UAAU,GAAG,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC;IAE7C,IAAM,WAAW,GAAG,YAAY,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;IAC7C,IAAM,GAAG,GAAG,WAAW,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC,MAAM,GAAG,CAAC,CAAC;IACrD,IAAM,oBAAoB,GAAG,WAAW,CAAC,GAAG,CACxC,UAAC,IAAI,EAAE,UAAU;QACb,OAAA,IAAI,CAAC,QAAQ,CAAC,CAAC,UAAU,GAAG,CAAC,CAAC,CAAC,QAAQ,EAAE,EAAE,GAAG,CAAC,GAAG,IAAI;IAAtD,CAAsD,CAAC,CAAC;IAChE,IAAI,aAAa,GAAG,CAAC,CAAC;IACtB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,oBAAoB,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;QACpD,aAAa,GAAG,IAAI,CAAC,GAAG,CAAC,oBAAoB,CAAC,CAAC,CAAC,CAAC,MAAM,EAAE,aAAa,CAAC,CAAC;KACzE;IAED,IAAM,gBAAgB,GAAG,oBAAoB,CAAC,KAAK,CAAC,CAAC,EAAE,UAAU,GAAG,CAAC,CAAC,CAAC;IACvE,IAAM,SAAS,GAAG,oBAAoB,CAAC,KAAK,CAAC,UAAU,GAAG,CAAC,EAAE,UAAU,CAAC,CAAC;IACzE,IAAM,eAAe,GAAG,oBAAoB,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;IAE/D,OAAO,CAAC,GAAG,CAAC,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;IACzC,OAAO,CAAC,GAAG,CAAC,aAAa,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC1C,OAAO,CAAC,GAAG,CACP,QAAM,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,aAAa,CAAG,EAClD,+DAA+D,CAAC,CAAC;IACrE,OAAO,CAAC,GAAG,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;AAC1C,CAAC;AAED,SAAgB,aAAa,CACzB,EAAyB,EAAE,KAAc;IAC3C,OAAO,WAAW,CACd,EAAE,EAAE,KAAK,EAAE,cAAM,OAAA,EAAE,CAAC,aAAa,EAAE,EAAlB,CAAkB,EAAE,gCAAgC,CAAC,CAAC;AAC7E,CAAC;AAJD,sCAIC;AAED,SAAgB,WAAW,CACvB,EAAyB,EAAE,KAAc,EAAE,OAAqB;IAClE,YAAY,CAAC,EAAE,EAAE,KAAK,EAAE,cAAM,OAAA,EAAE,CAAC,WAAW,CAAC,OAAO,CAAC,EAAvB,CAAuB,CAAC,CAAC;IACvD,IAAI,EAAE,CAAC,mBAAmB,CAAC,OAAO,EAAE,EAAE,CAAC,WAAW,CAAC,KAAK,KAAK,EAAE;QAC7D,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC,CAAC;QAC3C,MAAM,IAAI,KAAK,CAAC,6CAA6C,CAAC,CAAC;KAChE;AACH,CAAC;AAPD,kCAOC;AAED,SAAgB,eAAe,CAC3B,EAAyB,EAAE,KAAc,EAAE,OAAqB;IAClE,YAAY,CAAC,EAAE,EAAE,KAAK,EAAE,cAAM,OAAA,EAAE,CAAC,eAAe,CAAC,OAAO,CAAC,EAA3B,CAA2B,CAAC,CAAC;IAC3D,IAAI,EAAE,CAAC,mBAAmB,CAAC,OAAO,EAAE,EAAE,CAAC,eAAe,CAAC,KAAK,KAAK,EAAE;QACjE,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC,CAAC;QAC3C,MAAM,IAAI,KAAK,CAAC,mCAAmC,CAAC,CAAC;KACtD;AACH,CAAC;AAPD,0CAOC;AAED,SAAgB,wBAAwB,CACpC,EAAyB,EAAE,KAAc,EACzC,IAAkB;IACpB,IAAM,MAAM,GAAgB,WAAW,CACnC,EAAE,EAAE,KAAK,EAAE,cAAM,OAAA,EAAE,CAAC,YAAY,EAAE,EAAjB,CAAiB,EAAE,8BAA8B,CAAC,CAAC;IACxE,YAAY,CAAC,EAAE,EAAE,KAAK,EAAE,cAAM,OAAA,EAAE,CAAC,UAAU,CAAC,EAAE,CAAC,YAAY,EAAE,MAAM,CAAC,EAAtC,CAAsC,CAAC,CAAC;IACtE,YAAY,CACR,EAAE,EAAE,KAAK,EAAE,cAAM,OAAA,EAAE,CAAC,UAAU,CAAC,EAAE,CAAC,YAAY,EAAE,IAAI,EAAE,EAAE,CAAC,WAAW,CAAC,EAApD,CAAoD,CAAC,CAAC;IAC3E,OAAO,MAAM,CAAC;AAChB,CAAC;AATD,4DASC;AAED,SAAgB,uBAAuB,CACnC,EAAyB,EAAE,KAAc,EAAE,IAAiB;IAC9D,IAAM,MAAM,GAAgB,WAAW,CACnC,EAAE,EAAE,KAAK,EAAE,cAAM,OAAA,EAAE,CAAC,YAAY,EAAE,EAAjB,CAAiB,EAAE,8BAA8B,CAAC,CAAC;IACxE,YAAY,CAAC,EAAE,EAAE,KAAK,EAAE,cAAM,OAAA,EAAE,CAAC,UAAU,CAAC,EAAE,CAAC,oBAAoB,EAAE,MAAM,CAAC,EAA9C,CAA8C,CAAC,CAAC;IAC9E,YAAY,CACR,EAAE,EAAE,KAAK,EACT,cAAM,OAAA,EAAE,CAAC,UAAU,CAAC,EAAE,CAAC,oBAAoB,EAAE,IAAI,EAAE,EAAE,CAAC,WAAW,CAAC,EAA5D,CAA4D,CAAC,CAAC;IACxE,OAAO,MAAM,CAAC;AAChB,CAAC;AATD,0DASC;AAED,SAAgB,cAAc;IAC5B,IAAI,iBAAG,EAAE,CAAC,SAAS,CAAC,eAAe,CAAC,KAAK,CAAC,EAAE;QAC1C,OAAO,CAAC,CAAC;KACV;IACD,OAAO,CAAC,CAAC;AACX,CAAC;AALD,wCAKC;AAED,SAAgB,aAAa,CACzB,EAAyB,EAAE,KAAc;IAC3C,OAAO,WAAW,CACd,EAAE,EAAE,KAAK,EAAE,cAAM,OAAA,EAAE,CAAC,aAAa,EAAE,EAAlB,CAAkB,EAAE,gCAAgC,CAAC,CAAC;AAC7E,CAAC;AAJD,sCAIC;AAED,SAAgB,mBAAmB,CAAC,KAAa,EAAE,MAAc;IAC/D,IAAM,cAAc,GAAG,iBAAG,EAAE,CAAC,SAAS,CAAC,wBAAwB,CAAC,CAAC;IACjE,IAAI,CAAC,KAAK,IAAI,CAAC,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC,CAAC,EAAE;QACjC,IAAM,SAAS,GAAG,MAAI,KAAK,SAAI,MAAM,MAAG,CAAC;QACzC,MAAM,IAAI,KAAK,CAAC,yBAAyB,GAAG,SAAS,GAAG,cAAc,CAAC,CAAC;KACzE;IACD,IAAI,CAAC,KAAK,GAAG,cAAc,CAAC,IAAI,CAAC,MAAM,GAAG,cAAc,CAAC,EAAE;QACzD,IAAM,SAAS,GAAG,MAAI,KAAK,SAAI,MAAM,MAAG,CAAC;QACzC,IAAM,GAAG,GAAG,MAAI,cAAc,SAAI,cAAc,MAAG,CAAC;QACpD,MAAM,IAAI,KAAK,CACX,yBAAyB,GAAG,SAAS;YACrC,oDAAoD,GAAG,GAAG,GAAG,GAAG,CAAC,CAAC;KACvE;AACH,CAAC;AAbD,kDAaC;AAED,SAAgB,iBAAiB,CAC7B,EAAyB,EAAE,KAAc;IAC3C,OAAO,WAAW,CACd,EAAE,EAAE,KAAK,EAAE,cAAM,OAAA,EAAE,CAAC,iBAAiB,EAAE,EAAtB,CAAsB,EACvC,oCAAoC,CAAC,CAAC;AAC5C,CAAC;AALD,8CAKC;AAED,SAAgB,kCAAkC,CAC9C,EAAyB,EAAE,KAAc,EAAE,OAAqB,EAChE,SAAiB,EAAE,MAAmB,EAAE,mBAA2B,EACnE,iBAAyB,EAAE,iBAAyB;IACtD,IAAM,GAAG,GAAG,EAAE,CAAC,iBAAiB,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC;IACrD,IAAI,GAAG,KAAK,CAAC,CAAC,EAAE;QACd,4EAA4E;QAC5E,wBAAwB;QACxB,OAAO,KAAK,CAAC;KACd;IACD,YAAY,CAAC,EAAE,EAAE,KAAK,EAAE,cAAM,OAAA,EAAE,CAAC,UAAU,CAAC,EAAE,CAAC,YAAY,EAAE,MAAM,CAAC,EAAtC,CAAsC,CAAC,CAAC;IACtE,YAAY,CACR,EAAE,EAAE,KAAK,EACT,cAAM,OAAA,EAAE,CAAC,mBAAmB,CACxB,GAAG,EAAE,mBAAmB,EAAE,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,iBAAiB,EAC5D,iBAAiB,CAAC,EAFhB,CAEgB,CAAC,CAAC;IAC5B,YAAY,CAAC,EAAE,EAAE,KAAK,EAAE,cAAM,OAAA,EAAE,CAAC,uBAAuB,CAAC,GAAG,CAAC,EAA/B,CAA+B,CAAC,CAAC;IAC/D,OAAO,IAAI,CAAC;AACd,CAAC;AAlBD,gFAkBC;AAED,SAAgB,eAAe,CAC3B,EAAyB,EAAE,KAAc,EAAE,OAAqB,EAChE,WAAmB;IACrB,mBAAmB,CAAC,EAAE,EAAE,WAAW,CAAC,CAAC;IACrC,YAAY,CAAC,EAAE,EAAE,KAAK,EAAE,cAAM,OAAA,EAAE,CAAC,aAAa,CAAC,EAAE,CAAC,QAAQ,GAAG,WAAW,CAAC,EAA3C,CAA2C,CAAC,CAAC;IAC3E,YAAY,CAAC,EAAE,EAAE,KAAK,EAAE,cAAM,OAAA,EAAE,CAAC,WAAW,CAAC,EAAE,CAAC,UAAU,EAAE,OAAO,CAAC,EAAtC,CAAsC,CAAC,CAAC;AACxE,CAAC;AAND,0CAMC;AAED,SAAgB,iBAAiB,CAC7B,EAAyB,EAAE,KAAc,EAAE,WAAmB;IAChE,mBAAmB,CAAC,EAAE,EAAE,WAAW,CAAC,CAAC;IACrC,YAAY,CAAC,EAAE,EAAE,KAAK,EAAE,cAAM,OAAA,EAAE,CAAC,aAAa,CAAC,EAAE,CAAC,QAAQ,GAAG,WAAW,CAAC,EAA3C,CAA2C,CAAC,CAAC;IAC3E,YAAY,CAAC,EAAE,EAAE,KAAK,EAAE,cAAM,OAAA,EAAE,CAAC,WAAW,CAAC,EAAE,CAAC,UAAU,EAAE,IAAI,CAAC,EAAnC,CAAmC,CAAC,CAAC;AACrE,CAAC;AALD,8CAKC;AAED,SAAgB,gCAAgC,CAC5C,EAAyB,EAAE,KAAc,EAAE,OAAqB,EAChE,WAAmB;IACrB,OAAO,WAAW,CACd,EAAE,EAAE,KAAK,EAAE,cAAM,OAAA,EAAE,CAAC,kBAAkB,CAAC,OAAO,EAAE,WAAW,CAAC,EAA3C,CAA2C,EAC5D,WAAW,GAAG,WAAW,GAAG,2BAA2B,CAAC,CAAC;AAC/D,CAAC;AAND,4EAMC;AAED,SAAgB,yBAAyB,CACrC,EAAyB,EAAE,OAAqB,EAChD,WAAmB;IACrB,OAAO,EAAE,CAAC,kBAAkB,CAAC,OAAO,EAAE,WAAW,CAAC,CAAC;AACrD,CAAC;AAJD,8DAIC;AAED,SAAgB,kCAAkC,CAC9C,EAAyB,EAAE,KAAc,EAAE,OAAqB,EAChE,OAAqB,EAAE,sBAA4C,EACnE,WAAmB;IACrB,YAAY,CACR,EAAE,EAAE,KAAK,EAAE,cAAM,OAAA,eAAe,CAAC,EAAE,EAAE,KAAK,EAAE,OAAO,EAAE,WAAW,CAAC,EAAhD,CAAgD,CAAC,CAAC;IACvE,YAAY,CACR,EAAE,EAAE,KAAK,EAAE,cAAM,OAAA,EAAE,CAAC,SAAS,CAAC,sBAAsB,EAAE,WAAW,CAAC,EAAjD,CAAiD,CAAC,CAAC;AAC1E,CAAC;AARD,gFAQC;AAED,SAAgB,uBAAuB,CACnC,EAAyB,EAAE,KAAc;IAC3C,YAAY,CAAC,EAAE,EAAE,KAAK,EAAE,cAAM,OAAA,EAAE,CAAC,eAAe,CAAC,EAAE,CAAC,WAAW,EAAE,IAAI,CAAC,EAAxC,CAAwC,CAAC,CAAC;IACxE,YAAY,CACR,EAAE,EAAE,KAAK,EAAE,cAAM,OAAA,EAAE,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,MAAM,CAAC,KAAK,EAAE,EAAE,CAAC,MAAM,CAAC,MAAM,CAAC,EAApD,CAAoD,CAAC,CAAC;IAC3E,YAAY,CACR,EAAE,EAAE,KAAK,EAAE,cAAM,OAAA,EAAE,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,MAAM,CAAC,KAAK,EAAE,EAAE,CAAC,MAAM,CAAC,MAAM,CAAC,EAAnD,CAAmD,CAAC,CAAC;AAC5E,CAAC;AAPD,0DAOC;AAED,SAAgB,6BAA6B,CACzC,EAAyB,EAAE,KAAc,EAAE,OAAqB,EAChE,WAA6B;IAC/B,YAAY,CACR,EAAE,EAAE,KAAK,EAAE,cAAM,OAAA,EAAE,CAAC,eAAe,CAAC,EAAE,CAAC,WAAW,EAAE,WAAW,CAAC,EAA/C,CAA+C,CAAC,CAAC;IACtE,YAAY,CACR,EAAE,EAAE,KAAK,EACT,cAAM,OAAA,EAAE,CAAC,oBAAoB,CACzB,EAAE,CAAC,WAAW,EAAE,EAAE,CAAC,iBAAiB,EAAE,EAAE,CAAC,UAAU,EAAE,OAAO,EAAE,CAAC,CAAC,EAD9D,CAC8D,CAAC,CAAC;AAC5E,CAAC;AATD,sEASC;AAED,SAAgB,iCAAiC,CAC7C,EAAyB,EAAE,KAAc,EAAE,WAA6B;IAC1E,YAAY,CACR,EAAE,EAAE,KAAK,EAAE,cAAM,OAAA,EAAE,CAAC,eAAe,CAAC,EAAE,CAAC,WAAW,EAAE,WAAW,CAAC,EAA/C,CAA+C,CAAC,CAAC;IACtE,YAAY,CACR,EAAE,EAAE,KAAK,EACT,cAAM,OAAA,EAAE,CAAC,oBAAoB,CACzB,EAAE,CAAC,WAAW,EAAE,EAAE,CAAC,iBAAiB,EAAE,EAAE,CAAC,UAAU,EAAE,IAAI,EAAE,CAAC,CAAC,EAD3D,CAC2D,CAAC,CAAC;AACzE,CAAC;AARD,8EAQC;AAED,SAAgB,mBAAmB,CAAC,EAAyB;IAC3D,IAAM,MAAM,GAAG,EAAE,CAAC,sBAAsB,CAAC,EAAE,CAAC,WAAW,CAAC,CAAC;IACzD,IAAI,MAAM,KAAK,EAAE,CAAC,oBAAoB,EAAE;QACtC,MAAM,IAAI,KAAK,CACX,6BAA6B,GAAG,0BAA0B,CAAC,EAAE,EAAE,MAAM,CAAC,CAAC,CAAC;KAC7E;AACH,CAAC;AAND,kDAMC;AAED,SAAgB,0BAA0B,CACtC,EAAyB,EAAE,MAAc;IAC3C,QAAQ,MAAM,EAAE;QACd,KAAK,EAAE,CAAC,iCAAiC;YACvC,OAAO,mCAAmC,CAAC;QAC7C,KAAK,EAAE,CAAC,yCAAyC;YAC/C,OAAO,2CAA2C,CAAC;QACrD,KAAK,EAAE,CAAC,iCAAiC;YACvC,OAAO,mCAAmC,CAAC;QAC7C,KAAK,EAAE,CAAC,uBAAuB;YAC7B,OAAO,yBAAyB,CAAC;QACnC;YACE,OAAO,mBAAiB,MAAQ,CAAC;KACpC;AACH,CAAC;AAdD,gEAcC;AAED,SAAS,WAAW,CAChB,EAAyB,EAAE,KAAc,EAAE,aAA6B,EACxE,cAAsB;IACxB,IAAM,OAAO,GAAW,YAAY,CAAC,EAAE,EAAE,KAAK,EAAE,cAAM,OAAA,aAAa,EAAE,EAAf,CAAe,CAAC,CAAC;IACvE,IAAI,OAAO,IAAI,IAAI,EAAE;QACnB,MAAM,IAAI,KAAK,CAAC,cAAc,CAAC,CAAC;KACjC;IACD,OAAO,OAAO,CAAC;AACjB,CAAC;AAED,SAAS,mBAAmB,CAAC,EAAyB,EAAE,WAAmB;IACzE,IAAM,cAAc,GAAG,EAAE,CAAC,gCAAgC,GAAG,CAAC,CAAC;IAC/D,IAAM,aAAa,GAAG,WAAW,GAAG,EAAE,CAAC,QAAQ,CAAC;IAChD,IAAI,aAAa,GAAG,EAAE,CAAC,QAAQ,IAAI,aAAa,GAAG,cAAc,EAAE;QACjE,IAAM,gBAAgB,GAAG,6BAA2B,cAAc,MAAG,CAAC;QACtE,MAAM,IAAI,KAAK,CAAC,4BAA0B,gBAAgB,MAAG,CAAC,CAAC;KAChE;AACH,CAAC;AAED,SAAgB,WAAW,CAAC,KAAe,EAAE,UAAc;IAAd,2BAAA,EAAA,cAAc;IACzD,OAAO,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,MAAM,GAAG,UAAU,CAAC,CAAC,CAAC;AACvE,CAAC;AAFD,kCAEC;AAED,SAAgB,WAAW,CAAC,KAAe;IACzC,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE;QACtB,MAAM,KAAK,CAAC,sDAAsD,CAAC,CAAC;KACrE;IAED,OAAO;QACL,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC;KACxE,CAAC;AACJ,CAAC;AARD,kCAQC;AAED,SAAgB,YAAY,CAAC,KAAe;IAC1C,IAAI,SAAS,GAA6B,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;IACpD,IAAM,QAAQ,GAAG,KAAK,CAAC,MAAM,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,KAAK,CAAC,IAAI,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC;IAC9E,IAAI,CAAC,QAAQ,EAAE;QACb,SAAS;YACL,CAAC,WAAW,CAAC,KAAK,CAAC,SAAK,WAAW,CAAC,KAAK,CAAC,CAA6B,CAAC;KAC7E;IACD,OAAO,SAAS,CAAC;AACnB,CAAC;AARD,oCAQC;AAED,SAAgB,+BAA+B,CAC3C,QAAkB,EAAE,QAAgB;;IAAhB,yBAAA,EAAA,gBAAgB;IACtC,IAAI,UAAU,GAAG,iBAAG,EAAE,CAAC,SAAS,CAAC,wBAAwB,CAAC,CAAC;IAC3D,IAAI,QAAQ,EAAE;QACZ,UAAU,GAAG,UAAU,GAAG,CAAC,CAAC;QAE5B,4EAA4E;QAC5E,0EAA0E;QAC1E,2EAA2E;QAC3E,0EAA0E;QAC1E,gEAAgE;QAChE,QAAQ,GAAG,QAAQ,CAAC,GAAG,CACnB,UAAC,CAAC,EAAE,CAAC,IAAK,OAAA,CAAC,IAAI,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;YAChC,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACrC,QAAQ,CAAC,CAAC,CAAC,EAFL,CAEK,CAAC,CAAC;QAErB,sEAAsE;QACtE,UAAU;QACV,IAAI,QAAQ,CAAC,MAAM,KAAK,CAAC,EAAE;YACzB,QAAQ,GAAG,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;SAC7B;KACF;IAED,4EAA4E;IAC5E,IAAI,QAAQ,CAAC,MAAM,KAAK,CAAC,EAAE;QACzB,IAAM,aAAa,GAAG,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;QAClD,QAAQ,GAAG,aAAa,CAAC,QAAQ,CAAC;KACnC;IAED,IAAI,IAAI,GAAG,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;IACxC,IAAI,QAAQ,CAAC,MAAM,IAAI,CAAC,IAAI,IAAI,IAAI,UAAU,EAAE;QAC9C,OAAO,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;KAClB;SAAM,IACH,QAAQ,CAAC,MAAM,KAAK,CAAC,IAAI,QAAQ,CAAC,CAAC,CAAC,IAAI,UAAU;QAClD,QAAQ,CAAC,CAAC,CAAC,IAAI,UAAU,EAAE;QAC7B,OAAO,QAA4B,CAAC;KACrC;SAAM,IACH,QAAQ,CAAC,MAAM,KAAK,CAAC,IAAI,QAAQ,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC,IAAI,UAAU;QAChE,QAAQ,CAAC,CAAC,CAAC,IAAI,UAAU,EAAE;QAC7B,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;KACjD;SAAM,IACH,QAAQ,CAAC,MAAM,KAAK,CAAC,IAAI,QAAQ,CAAC,CAAC,CAAC,IAAI,UAAU;QAClD,QAAQ,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC,IAAI,UAAU,EAAE;QAC3C,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;KACjD;SAAM,IACH,QAAQ,CAAC,MAAM,KAAK,CAAC;QACrB,QAAQ,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC,IAAI,UAAU;QACrD,QAAQ,CAAC,CAAC,CAAC,IAAI,UAAU,EAAE;QAC7B,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;KAC/D;SAAM,IACH,QAAQ,CAAC,MAAM,KAAK,CAAC,IAAI,QAAQ,CAAC,CAAC,CAAC,IAAI,UAAU;QAClD,QAAQ,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC,IAAI,UAAU,EAAE;QACzD,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;KAC/D;SAAM;QACL,IAAI,QAAQ,EAAE;YACZ,qEAAqE;YACrE,uEAAuE;YACvE,qEAAqE;YACrE,mEAAmE;YACnE,+BAA+B;YAE/B,IAAM,QAAQ,GAAG,WAAW,CAAC,QAAQ,CAAC,CAAC;YACvC,IAAI,IAAI,GAAG,CAAC,EAAE,IAAI,GAAG,CAAC,CAAC;YACvB,IAAI,QAAQ,CAAC,MAAM,EAAE;gBACnB,0BAAoC,EAAnC,YAAI,EAAE,YAAI,CAA0B;aACtC;YACD,IAAI,GAAG,QAAQ,GAAG,CAAC,IAAI,GAAG,CAAC,CAAC,GAAG,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC;YAC1C,OAAO,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,GAAG,CAAC,EAAL,CAAK,CAAqB,CAAC;SAC3E;QACD,OAAO,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAC;KACvC;AACH,CAAC;AAvED,0EAuEC;AAED,SAAS,MAAM,CAAC,CAAS;IACvB,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;AACrB,CAAC;AAED;;;GAGG;AACH,SAAgB,aAAa,CAAC,MAAgB,EAAE,MAAgB;IAC9D,MAAM,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;IAC1B,MAAM,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;IAE1B,IAAI,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,MAAM,CAAC,EAAE;QACpC,OAAO,IAAI,CAAC;KACb;IAED,IAAI,CAAC,MAAM,CAAC,MAAM,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,EAAG,iCAAiC;QACxE,OAAO,IAAI,CAAC;KACb;IAED,IAAI,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC;QACrD,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE;QACnB,OAAO,IAAI,CAAC;KACb;IAED,IAAI,MAAM,CAAC,MAAM,KAAK,MAAM,CAAC,MAAM,EAAE,EAAG,iCAAiC;QACvE,IAAM,UAAU,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACvC,IAAM,UAAU,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACvC,IAAI,UAAU,KAAK,UAAU,EAAE;YAC7B,OAAO,IAAI,CAAC;SACb;QAED,IAAI,MAAM,CAAC,UAAU,CAAC,IAAI,MAAM,CAAC,UAAU,CAAC;YACxC,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE;YACxC,OAAO,IAAI,CAAC;SACb;KACF;IACD,OAAO,MAAM,CAAC,CAAC,CAAC,KAAK,MAAM,CAAC,CAAC,CAAC,IAAI,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;AAC3E,CAAC;AA9BD,sCA8BC;AAED,mEAAmE;AACnE,wEAAwE;AACxE,oBAAoB;AACpB,IAAI,gBAAwB,CAAC;AAC7B,IAAI,sBAA8B,CAAC;AAEnC,SAAgB,sBAAsB,CAAC,YAAoB;IACzD,IAAI,gBAAgB,IAAI,IAAI,EAAE;QAC5B,IAAM,EAAE,GAAG,6BAAe,CAAC,YAAY,CAAC,CAAC;QACzC,gBAAgB,GAAG,EAAE,CAAC,YAAY,CAAC,EAAE,CAAC,gBAAgB,CAAC,CAAC;KACzD;IACD,OAAO,gBAAgB,CAAC;AAC1B,CAAC;AAND,wDAMC;AAED,SAAgB,mBAAmB;IACjC,gBAAgB,GAAG,IAAI,CAAC;AAC1B,CAAC;AAFD,kDAEC;AACD,SAAgB,wBAAwB;IACtC,sBAAsB,GAAG,IAAI,CAAC;AAChC,CAAC;AAFD,4DAEC;AAED,SAAgB,sBAAsB,CAAC,YAAoB;IACzD,IAAI,sBAAsB,IAAI,IAAI,EAAE;QAClC,IAAM,EAAE,GAAG,6BAAe,CAAC,YAAY,CAAC,CAAC;QACzC,sBAAsB,GAAG,EAAE,CAAC,YAAY,CAAC,EAAE,CAAC,uBAAuB,CAAC,CAAC;KACtE;IACD,mEAAmE;IACnE,OAAO,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,sBAAsB,CAAC,CAAC;AAC9C,CAAC;AAPD,wDAOC;AAED,SAAgB,iCAAiC,CAAC,YAAoB;IAEpE,IAAI,YAAY,KAAK,CAAC,EAAE;QACtB,OAAO,CAAC,CAAC;KACV;IAED,IAAI,iBAAyB,CAAC;IAC9B,IAAM,EAAE,GAAG,6BAAe,CAAC,YAAY,CAAC,CAAC;IAEzC,IAAI,YAAY,CAAC,EAAE,EAAE,iCAAiC,CAAC;QACnD,YAAY,KAAK,CAAC,EAAE;QACtB,iBAAiB,GAAG,CAAC,CAAC;KACvB;SAAM,IAAI,YAAY,CAAC,EAAE,EAAE,0BAA0B,CAAC,EAAE;QACvD,iBAAiB,GAAG,CAAC,CAAC;KACvB;SAAM;QACL,iBAAiB,GAAG,CAAC,CAAC;KACvB;IACD,OAAO,iBAAiB,CAAC;AAC3B,CAAC;AAlBD,8EAkBC;AAED,SAAgB,YAAY,CAAC,EAAyB,EAAE,aAAqB;IAC3E,IAAM,GAAG,GAAG,EAAE,CAAC,YAAY,CAAC,aAAa,CAAC,CAAC;IAC3C,OAAO,GAAG,IAAI,IAAI,CAAC;AACrB,CAAC;AAHD,oCAGC;AAED,SAAgB,qBAAqB,CAAC,YAAiB;IACrD,IAAI;QACF,IAAM,EAAE,GAAG,6BAAe,CAAC,YAAY,CAAC,CAAC;QACzC,IAAI,EAAE,IAAI,IAAI,EAAE;YACd,OAAO,IAAI,CAAC;SACb;KACF;IAAC,OAAO,CAAC,EAAE;QACV,OAAO,KAAK,CAAC;KACd;IACD,OAAO,KAAK,CAAC;AACf,CAAC;AAVD,sDAUC;AAED,SAAgB,kCAAkC,CAAC,YAAoB;IAErE,IAAI,YAAY,KAAK,CAAC,EAAE;QACtB,OAAO,KAAK,CAAC;KACd;IAED,IAAM,EAAE,GAAG,6BAAe,CAAC,YAAY,CAAC,CAAC;IAEzC,IAAI,YAAY,KAAK,CAAC,EAAE;QACtB,IAAI,CAAC,YAAY,CAAC,EAAE,EAAE,mBAAmB,CAAC,EAAE;YAC1C,OAAO,KAAK,CAAC;SACd;KACF;SAAM;QACL,IAAI,CAAC,YAAY,CAAC,EAAE,EAAE,wBAAwB,CAAC,EAAE;YAC/C,OAAO,KAAK,CAAC;SACd;KACF;IAED,IAAM,qBAAqB,GAAG,sCAAsC,CAAC,EAAE,CAAC,CAAC;IACzE,OAAO,qBAAqB,CAAC;AAC/B,CAAC;AApBD,gFAoBC;AAED;;;;;;;;GAQG;AACH,SAAgB,6BAA6B,CAAC,YAAoB;IAChE,IAAI,YAAY,KAAK,CAAC,EAAE;QACtB,OAAO,KAAK,CAAC;KACd;IAED,IAAM,EAAE,GAAG,6BAAe,CAAC,YAAY,CAAC,CAAC;IAEzC,IAAI,YAAY,KAAK,CAAC,EAAE;QACtB,IAAI,CAAC,YAAY,CAAC,EAAE,EAAE,mBAAmB,CAAC,EAAE;YAC1C,OAAO,KAAK,CAAC;SACd;QACD,IAAI,CAAC,YAAY,CAAC,EAAE,EAAE,0BAA0B,CAAC,EAAE;YACjD,OAAO,KAAK,CAAC;SACd;KACF;SAAM;QACL,IAAI,YAAY,CAAC,EAAE,EAAE,wBAAwB,CAAC,EAAE;YAC9C,OAAO,sCAAsC,CAAC,EAAE,CAAC,CAAC;SACnD;QAED,IAAM,uBAAuB,GAAG,6BAA6B,CAAC;QAC9D,IAAI,YAAY,CAAC,EAAE,EAAE,uBAAuB,CAAC,EAAE;YAC7C,IAAM,yBAAyB,GAC3B,EAAE,CAAC,YAAY,CAAC,uBAAuB,CAAC,CAAC;YAC7C,OAAO,0CAA0C,CAC7C,EAAE,EAAE,yBAAyB,CAAC,CAAC;SACpC;QAED,OAAO,KAAK,CAAC;KACd;IAED,IAAM,qBAAqB,GAAG,sCAAsC,CAAC,EAAE,CAAC,CAAC;IACzE,OAAO,qBAAqB,CAAC;AAC/B,CAAC;AAhCD,sEAgCC;AAED,SAAS,sCAAsC,CAAC,EAAyB;IAEvE,IAAM,SAAS,GAAG,2BAAgB,CAAC,EAAE,CAAC,CAAC;IAEvC,IAAM,OAAO,GAAG,EAAE,CAAC,aAAa,EAAE,CAAC;IACnC,EAAE,CAAC,WAAW,CAAC,EAAE,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;IAEvC,IAAM,KAAK,GAAG,CAAC,CAAC;IAChB,IAAM,MAAM,GAAG,CAAC,CAAC;IACjB,EAAE,CAAC,UAAU,CACT,EAAE,CAAC,UAAU,EAAE,CAAC,EAAE,SAAS,CAAC,mBAAmB,EAAE,KAAK,EAAE,MAAM,EAAE,CAAC,EACjE,SAAS,CAAC,kBAAkB,EAAE,SAAS,CAAC,gBAAgB,EAAE,IAAI,CAAC,CAAC;IAEpE,IAAM,WAAW,GAAG,EAAE,CAAC,iBAAiB,EAAE,CAAC;IAC3C,EAAE,CAAC,eAAe,CAAC,EAAE,CAAC,WAAW,EAAE,WAAW,CAAC,CAAC;IAChD,EAAE,CAAC,oBAAoB,CACnB,EAAE,CAAC,WAAW,EAAE,EAAE,CAAC,iBAAiB,EAAE,EAAE,CAAC,UAAU,EAAE,OAAO,EAAE,CAAC,CAAC,CAAC;IAErE,IAAM,qBAAqB,GACvB,EAAE,CAAC,sBAAsB,CAAC,EAAE,CAAC,WAAW,CAAC,KAAK,EAAE,CAAC,oBAAoB,CAAC;IAE1E,EAAE,CAAC,WAAW,CAAC,EAAE,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC;IACpC,EAAE,CAAC,eAAe,CAAC,EAAE,CAAC,WAAW,EAAE,IAAI,CAAC,CAAC;IACzC,EAAE,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;IAC1B,EAAE,CAAC,iBAAiB,CAAC,WAAW,CAAC,CAAC;IAElC,OAAO,qBAAqB,CAAC;AAC/B,CAAC;AAED,SAAS,0CAA0C;AAC/C,kCAAkC;AAClC,EAAyB,EAAE,yBAA8B;IAC3D,IAAM,SAAS,GAAG,2BAAgB,CAAC,EAAE,EAAE,yBAAyB,CAAC,CAAC;IAClE,IAAM,OAAO,GAAG,EAAE,CAAC,aAAa,EAAE,CAAC;IACnC,EAAE,CAAC,WAAW,CAAC,EAAE,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;IAEvC,IAAM,KAAK,GAAG,CAAC,CAAC;IAChB,IAAM,MAAM,GAAG,CAAC,CAAC;IACjB,EAAE,CAAC,UAAU,CACT,EAAE,CAAC,UAAU,EAAE,CAAC,EAAE,SAAS,CAAC,uBAAuB,EAAE,KAAK,EAAE,MAAM,EAAE,CAAC,EACrE,SAAS,CAAC,kBAAkB,EAAE,SAAS,CAAC,oBAAoB,EAAE,IAAI,CAAC,CAAC;IAExE,IAAM,WAAW,GAAG,EAAE,CAAC,iBAAiB,EAAE,CAAC;IAC3C,EAAE,CAAC,eAAe,CAAC,EAAE,CAAC,WAAW,EAAE,WAAW,CAAC,CAAC;IAChD,EAAE,CAAC,oBAAoB,CACnB,EAAE,CAAC,WAAW,EAAE,EAAE,CAAC,iBAAiB,EAAE,EAAE,CAAC,UAAU,EAAE,OAAO,EAAE,CAAC,CAAC,CAAC;IAErE,IAAM,qBAAqB,GACvB,EAAE,CAAC,sBAAsB,CAAC,EAAE,CAAC,WAAW,CAAC,KAAK,EAAE,CAAC,oBAAoB,CAAC;IAE1E,EAAE,CAAC,WAAW,CAAC,EAAE,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC;IACpC,EAAE,CAAC,eAAe,CAAC,EAAE,CAAC,WAAW,EAAE,IAAI,CAAC,CAAC;IACzC,EAAE,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;IAC1B,EAAE,CAAC,iBAAiB,CAAC,WAAW,CAAC,CAAC;IAElC,OAAO,qBAAqB,CAAC;AAC/B,CAAC;AAED,SAAgB,mBAAmB,CAAC,YAAoB;IACtD,IAAI,YAAY,KAAK,CAAC,EAAE;QACtB,OAAO,KAAK,CAAC;KACd;IACD,IAAM,EAAE,GAAG,6BAAe,CAAC,YAAY,CAAC,CAAC;IAEzC,kCAAkC;IAClC,IAAM,SAAS,GAAI,EAAU,CAAC,SAAS,IAAI,IAAI,CAAC;IAChD,OAAO,SAAS,CAAC;AACnB,CAAC;AATD,kDASC"}