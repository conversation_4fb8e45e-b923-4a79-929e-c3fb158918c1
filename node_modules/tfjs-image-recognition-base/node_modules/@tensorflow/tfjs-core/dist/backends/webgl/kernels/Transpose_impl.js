"use strict";
/**
 * @license
 * Copyright 2020 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 * =============================================================================
 */
Object.defineProperty(exports, "__esModule", { value: true });
var environment_1 = require("../../../environment");
var transpose_gpu_1 = require("../transpose_gpu");
var transpose_packed_gpu_1 = require("../transpose_packed_gpu");
function transposeImpl(x, perm, backend) {
    var program = environment_1.env().getBool('WEBGL_PACK_ARRAY_OPERATIONS') ?
        new transpose_packed_gpu_1.TransposePackedProgram(x.shape, perm) :
        new transpose_gpu_1.TransposeProgram(x.shape, perm);
    return backend.runWebGLProgram(program, [x], x.dtype);
}
exports.transposeImpl = transposeImpl;
//# sourceMappingURL=Transpose_impl.js.map