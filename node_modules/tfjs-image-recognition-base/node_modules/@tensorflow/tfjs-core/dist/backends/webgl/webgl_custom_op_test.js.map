{"version": 3, "file": "webgl_custom_op_test.js", "sourceRoot": "", "sources": ["../../../src/backends/webgl/webgl_custom_op_test.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;;GAeG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEH,iBAqFA;;AArFA,gCAAkC;AAClC,mDAAqD;AAErD,6CAAkD;AAClD,6EAAyD;AAEzD,gCAAiB,CAAC,iBAAiB,EAAE,wCAAU,EAAE;IAC/C;QAIE,4BAAY,UAAoB;YAHhC,kBAAa,GAAG,CAAC,GAAG,CAAC,CAAC;YAIpB,IAAI,CAAC,WAAW,GAAG,UAAU,CAAC,KAAK,EAAE,CAAC;YAEtC,IAAI,CAAC,QAAQ,GAAG,iKAMb,CAAC;QACN,CAAC;QACH,yBAAC;IAAD,CAAC,AAfD,IAeC;IAED;QAIE,oCAAY,UAAoB;YAHhC,kBAAa,GAAG,CAAC,GAAG,CAAC,CAAC;YAIpB,IAAI,CAAC,WAAW,GAAG,UAAU,CAAC,KAAK,EAAE,CAAC;YAEtC,IAAI,CAAC,QAAQ,GAAG,qKAMb,CAAC;QACN,CAAC;QACH,iCAAC;IAAD,CAAC,AAfD,IAeC;IAED,SAAS,YAAY,CAAsB,CAAI;QAC7C,IAAM,EAAE,GAAG,EAAE,CAAC,UAAU,CAAC,UAAC,CAAI,EAAE,IAAqB;YACnD,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACV,IAAM,YAAY,GAAG,EAAE,CAAC,OAAO,EAA+B,CAAC;YAC/D,IAAM,OAAO,GAAG,IAAI,kBAAkB,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;YAChD,IAAM,eAAe,GAAG,IAAI,0BAA0B,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;YAEhE,IAAM,KAAK,GAAM,YAAY,CAAC,aAAa,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;YAE1D,IAAM,QAAQ,GAAG,UAAC,EAAK,EAAE,KAAe;gBAC/B,IAAA,YAAC,CAAU;gBAClB,IAAM,IAAI,GAAM,YAAY,CAAC,aAAa,CAAC,eAAe,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;gBACjE,OAAO,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;YACtB,CAAC,CAAC;YACF,OAAO,EAAC,KAAK,OAAA,EAAE,QAAQ,UAAA,EAAC,CAAC;QAC3B,CAAC,CAAC,CAAC;QACH,OAAO,EAAE,CAAC,CAAC,CAAC,CAAC;IACf,CAAC;IAED,EAAE,CAAC,kCAAkC,EAAE;;;;;oBAC/B,QAAQ,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;oBACxB,KAAK,GAAG,EAAE,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;oBAC5B,MAAM,GAAG,YAAY,CAAC,KAAK,CAAC,CAAC;oBACnC,KAAA,6BAAiB,CAAA;oBAAC,qBAAM,MAAM,CAAC,IAAI,EAAE,EAAA;;oBAArC,kBAAkB,SAAmB,EAAE,QAAQ,CAAC,GAAG,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,GAAG,CAAC,GAAG,CAAC,EAAT,CAAS,CAAC,EAAC,CAAC;;;;SACtE,CAAC,CAAC;IAEH,EAAE,CAAC,4CAA4C,EAAE;;;;;oBACzC,QAAQ,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;oBACxB,KAAK,GAAG,EAAE,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;oBAC5B,KAAK,GAAG,EAAE,CAAC,YAAY,CAAC,UAAA,CAAC,IAAI,OAAA,YAAY,CAAC,CAAC,CAAC,EAAf,CAAe,CAAC,CAAC;oBAC9C,KAAgB,KAAK,CAAC,KAAK,CAAC,EAA3B,KAAK,WAAA,EAAE,IAAI,UAAA,CAAiB;oBACnC,KAAA,6BAAiB,CAAA;oBAAC,qBAAM,KAAK,CAAC,IAAI,EAAE,EAAA;;oBAApC,kBAAkB,SAAkB,EAAE,QAAQ,CAAC,GAAG,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,GAAG,CAAC,GAAG,CAAC,EAAT,CAAS,CAAC,EAAC,CAAC;oBACpE,KAAA,6BAAiB,CAAA;oBAAC,qBAAM,IAAI,CAAC,IAAI,EAAE,EAAA;;oBAAnC,kBAAkB,SAAiB,EAAE,QAAQ,CAAC,GAAG,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,GAAG,CAAC,GAAG,CAAC,EAAT,CAAS,CAAC,EAAC,CAAC;;;;SACpE,CAAC,CAAC;IAEH,EAAE,CAAC,8CAA8C,EAAE;;;;;oBAC3C,QAAQ,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;oBACxB,KAAK,GAAG,EAAE,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;oBAC5B,KAAK,GAAG,EAAE,CAAC,YAAY,CAAC,UAAA,CAAC,IAAI,OAAA,YAAY,CAAC,CAAC,CAAC,EAAf,CAAe,CAAC,CAAC;oBAC9C,KAAgB,KAAK,CAAC,KAAK,EAAE,EAAE,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,EAAhD,KAAK,WAAA,EAAE,IAAI,UAAA,CAAsC;oBACxD,KAAA,6BAAiB,CAAA;oBAAC,qBAAM,KAAK,CAAC,IAAI,EAAE,EAAA;;oBAApC,kBAAkB,SAAkB,EAAE,QAAQ,CAAC,GAAG,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,GAAG,CAAC,GAAG,CAAC,EAAT,CAAS,CAAC,EAAC,CAAC;oBACpE,KAAA,6BAAiB,CAAA;oBAAC,qBAAM,IAAI,CAAC,IAAI,EAAE,EAAA;;oBAAnC,kBAAkB,SAAiB,EAAE,QAAQ,CAAC,GAAG,CAAC,cAAM,OAAA,GAAG,EAAH,CAAG,CAAC,EAAC,CAAC;;;;SAC/D,CAAC,CAAC;AACL,CAAC,CAAC,CAAC"}