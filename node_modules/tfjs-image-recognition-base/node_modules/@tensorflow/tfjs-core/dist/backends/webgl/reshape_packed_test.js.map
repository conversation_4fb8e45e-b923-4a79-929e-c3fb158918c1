{"version": 3, "file": "reshape_packed_test.js", "sourceRoot": "", "sources": ["../../../src/backends/webgl/reshape_packed_test.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;;GAeG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEH,iBAiFA;;AAjFA,gCAAkC;AAClC,mDAAqD;AACrD,6CAAkD;AAClD,6EAA0D;AAE1D,gCAAiB,CAAC,mBAAmB,EAAE,yCAAW,EAAE;IAClD,IAAM,OAAO,GACT,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;IAC3E,IAAI,CAAY,CAAC;IAEjB,UAAU,CAAC;QACT,IAAM,CAAC,GAAG,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QAC3D,IAAM,CAAC,GAAG,EAAE,CAAC,QAAQ,CACjB,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACjE,CAAC,GAAG,EAAE,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACtB,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,WAAW,EAAE;;;;;oBACR,KAAK,GAAG,EAAE,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;oBAC1C,KAAK,GAAG,EAAE,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC;oBACtD,KAAA,6BAAiB,CAAA;oBAAC,qBAAM,KAAK,CAAC,IAAI,EAAE,EAAA;;oBAApC,kBAAkB,SAAkB,EAAE,OAAO,EAAC,CAAC;;;;SAChD,CAAC,CAAC;IACH,EAAE,CAAC,WAAW,EAAE;;;;;oBACR,KAAK,GAAG,EAAE,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC;oBACxC,KAAK,GAAG,EAAE,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;oBACzC,KAAA,6BAAiB,CAAA;oBAAC,qBAAM,KAAK,CAAC,IAAI,EAAE,EAAA;;oBAApC,kBAAkB,SAAkB,EAAE,OAAO,EAAC,CAAC;;;;SAChD,CAAC,CAAC;IACH,EAAE,CAAC,WAAW,EAAE;;;;;oBACR,KAAK,GAAG,EAAE,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;oBACvC,KAAA,6BAAiB,CAAA;oBAAC,qBAAM,KAAK,CAAC,IAAI,EAAE,EAAA;;oBAApC,kBAAkB,SAAkB,EAAE,OAAO,EAAC,CAAC;;;;SAChD,CAAC,CAAC;IACH,EAAE,CAAC,WAAW,EAAE;;;;;oBACR,KAAK,GAAG,EAAE,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;oBACjC,KAAK,GAAG,EAAE,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;oBAC9C,KAAA,6BAAiB,CAAA;oBAAC,qBAAM,KAAK,CAAC,IAAI,EAAE,EAAA;;oBAApC,kBAAkB,SAAkB,EAAE,OAAO,EAAC,CAAC;;;;SAChD,CAAC,CAAC;IACH,EAAE,CAAC,WAAW,EAAE;;;;;oBACR,KAAK,GAAG,EAAE,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;oBACpC,KAAK,GAAG,EAAE,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;oBACjD,KAAA,6BAAiB,CAAA;oBAAC,qBAAM,KAAK,CAAC,IAAI,EAAE,EAAA;;oBAApC,kBAAkB,SAAkB,EAAE,OAAO,EAAC,CAAC;;;;SAChD,CAAC,CAAC;IACH,EAAE,CAAC,WAAW,EAAE;;;;;oBACR,KAAK,GAAG,EAAE,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;oBACvC,KAAK,GAAG,EAAE,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;oBACpD,KAAA,6BAAiB,CAAA;oBAAC,qBAAM,KAAK,CAAC,IAAI,EAAE,EAAA;;oBAApC,kBAAkB,SAAkB,EAAE,OAAO,EAAC,CAAC;;;;SAChD,CAAC,CAAC;AACL,CAAC,CAAC,CAAC;AAEH,gCAAiB,CAAC,qCAAqC,EAAE,yCAAW,EAAE;IACpE,EAAE,CAAC,iBAAiB,EAAE;;;;;oBACd,cAAc,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,SAAS,CAAC,wBAAwB,CAAC,CAAC;oBAEhE,MAAM,GAAa,IAAI,KAAK,CAAS,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;oBACrD,MAAM,GAAG,MAAM,CAAC,GAAG,CAAC,UAAC,CAAC,EAAE,CAAC,IAAK,OAAA,CAAC,GAAG,CAAC,EAAL,CAAK,CAAC,CAAC;oBAC/B,CAAC,GAAG,EAAE,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;oBAChC,CAAC,GAAG,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;oBAE5C,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,wBAAwB,EAAE,CAAC,CAAC,CAAC;oBAQpC,CAAC,GAAG,EAAE,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;oBACtB,KAAK,GAAG,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;oBACpC,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,wBAAwB,EAAE,cAAc,CAAC,CAAC;oBAGjD,kBAAkB,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;oBAC1D,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,YAAY,EAAE,KAAK,CAAC,CAAC;oBAClC,KAAK,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;oBACrB,KAAK,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;oBACtB,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,YAAY,EAAE,kBAAkB,CAAC,CAAC;oBAEzC,MAAM,GACR,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;oBACpE,KAAA,6BAAiB,CAAA;oBAAC,qBAAM,KAAK,CAAC,IAAI,EAAE,EAAA;;oBAApC,kBAAkB,SAAkB,EAAE,MAAM,EAAC,CAAC;;;;SAC/C,CAAC,CAAC;AACL,CAAC,CAAC,CAAC"}