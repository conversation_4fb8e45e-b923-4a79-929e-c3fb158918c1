{"version": 3, "file": "tex_util.js", "sourceRoot": "", "sources": ["../../../src/backends/webgl/tex_util.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;;GAeG;;AAEH,iDAAsC;AAItC,iCAAmC;AAEnC,IAAY,aAmCX;AAnCD,WAAY,aAAa;IACvB;;;;;;;;;;;;;;OAcG;IACH,mDAAK,CAAA;IAEL;;;;;;;;;;;;;;;OAeG;IACH,iEAAY,CAAA;AACd,CAAC,EAnCW,aAAa,GAAb,qBAAa,KAAb,qBAAa,QAmCxB;AAED,IAAY,YAKX;AALD,WAAY,YAAY;IACtB,mDAAM,CAAA;IACN,mDAAM,CAAA;IACN,mDAAM,CAAA;IACN,uDAAQ,CAAA;AACV,CAAC,EALW,YAAY,GAAZ,oBAAY,KAAZ,oBAAY,QAKvB;AAED,IAAY,mBAMX;AAND,WAAY,mBAAmB;IAC7B,qFAAgB,CAAA;IAChB,qFAAgB,CAAA;IAChB,qGAAwB,CAAA;IACxB,yFAAkB,CAAA;IAClB,yFAAkB,CAAA;AACpB,CAAC,EANW,mBAAmB,GAAnB,2BAAmB,KAAnB,2BAAmB,QAM9B;AA4BD,SAAgB,wCAAwC,CACpD,IAAY,EAAE,OAAe;IAC/B,OAAO,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;AACzB,CAAC;AAHD,4FAGC;AAED,SAAgB,kCAAkC,CAC9C,UAAkB,EAAE,kBAA0B;IAChD,OAAO,UAAU,GAAG,kBAAkB,CAAC;AACzC,CAAC;AAHD,gFAGC;AAED,SAAgB,qCAAqC,CACjD,IAAY,EAAE,OAAe;IAC/B,OAAO,CAAC,OAAO,GAAG,CAAC,EAAE,IAAI,CAAC,CAAC;AAC7B,CAAC;AAHD,sFAGC;AAED;;GAEG;AACH,SAAgB,gBAAgB,CAAC,KAAe;IAC9C,IAAM,IAAI,GAAG,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;IACvC,IAAM,YAAY,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC;IACzC,OAAO,IAAI,CAAC,mBAAmB,CAAC,YAAY,CAAC,CAAC;AAChD,CAAC;AAJD,4CAIC;AAED,SAAgB,kCAAkC,CAC9C,YAAoB,EAAE,kBAA0B;IAClD,IAAI,YAAY,GAAG,kBAAkB,KAAK,CAAC,EAAE;QAC3C,MAAM,IAAI,KAAK,CACX,mBAAiB,YAAY,6BAA0B;aACvD,KAAG,kBAAoB,CAAA,CAAC,CAAC;KAC9B;IACD,OAAO,YAAY,GAAG,kBAAkB,CAAC;AAC3C,CAAC;AARD,gFAQC;AAED,SAAgB,sCAAsC,CAClD,aAA2B,EAAE,MAAoB,EAAE,QAAgB;IACrE,IAAM,YAAY,GAAG,aAAa,CAAC,MAAM,GAAG,QAAQ,GAAG,CAAC,CAAC;IACzD,IAAI,MAAM,CAAC,MAAM,GAAG,YAAY,EAAE;QAChC,MAAM,IAAI,KAAK,CACX,oBAAkB,MAAM,CAAC,MAAM,qBAAgB,YAAc,CAAC,CAAC;KACpE;IACD,IAAI,GAAG,GAAG,CAAC,CAAC;IACZ,KAAK,IAAI,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,aAAa,CAAC,MAAM,EAAE,GAAG,IAAI,CAAC,EAAE;QACtD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,EAAE,CAAC,EAAE,EAAE;YACjC,MAAM,CAAC,GAAG,EAAE,CAAC,GAAG,aAAa,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;SACxC;KACF;AACH,CAAC;AAbD,wFAaC;AAED,SAAgB,sCAAsC,CAClD,IAAY,EAAE,OAAe;IAC/B,OAAO;QACL,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,OAAO,GAAG,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC;KACtE,CAAC;AACJ,CAAC;AALD,wFAKC;AAED,SAAgB,qCAAqC,CACjD,IAAY,EAAE,OAAe;IACzB,IAAA,0DAA8D,EAA7D,SAAC,EAAE,SAA0D,CAAC;IACrE,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AACnB,CAAC;AAJD,sFAIC;AAmBD,SAAgB,gBAAgB;AAC5B,kCAAkC;AAClC,EAAyB,EAAE,yBAA+B;IAC5D,kCAAkC;IAClC,IAAM,KAAK,GAAG,EAAS,CAAC;IAExB,IAAI,mBAA2B,CAAC;IAChC,IAAI,uBAA+B,CAAC;IACpC,IAAI,6BAAqC,CAAC;IAC1C,IAAI,yBAAiC,CAAC;IACtC,IAAI,kBAA0B,CAAC;IAE/B,IAAI,qBAA6B,CAAC;IAClC,IAAI,yBAAiC,CAAC;IAEtC,IAAI,kBAA0B,CAAC;IAC/B,IAAI,oBAA4B,CAAC;IACjC,IAAI,gBAAwB,CAAC;IAE7B,IAAI,iBAAG,EAAE,CAAC,SAAS,CAAC,eAAe,CAAC,KAAK,CAAC,EAAE;QAC1C,mBAAmB,GAAG,KAAK,CAAC,IAAI,CAAC;QACjC,uBAAuB,GAAG,KAAK,CAAC,IAAI,CAAC;QACrC,6BAA6B,GAAG,KAAK,CAAC,OAAO,CAAC;QAC9C,yBAAyB,GAAG,KAAK,CAAC,OAAO,CAAC;QAC1C,kBAAkB,GAAG,KAAK,CAAC,GAAG,CAAC;QAC/B,yBAAyB,GAAG,CAAC,CAAC;QAC9B,kBAAkB,GAAG,CAAC,CAAC;QACvB,oBAAoB,GAAG,KAAK,CAAC,UAAU,CAAC;QACxC,gBAAgB,GAAG,KAAK,CAAC,KAAK,CAAC;KAChC;SAAM;QACL,mBAAmB,GAAG,EAAE,CAAC,IAAI,CAAC;QAC9B,uBAAuB,GAAG,EAAE,CAAC,IAAI,CAAC;QAClC,6BAA6B,GAAG,EAAE,CAAC,IAAI,CAAC;QACxC,yBAAyB,GAAG,KAAK,CAAC,IAAI,CAAC;QACvC,kBAAkB,GAAG,EAAE,CAAC,IAAI,CAAC;QAC7B,yBAAyB,GAAG,CAAC,CAAC;QAC9B,kBAAkB,GAAG,CAAC,CAAC;QACvB,oBAAoB,GAAG,yBAAyB,IAAI,IAAI,CAAC,CAAC;YACtD,yBAAyB,CAAC,cAAc,CAAC,CAAC;YAC1C,IAAI,CAAC;QACT,gBAAgB,GAAG,EAAE,CAAC,KAAK,CAAC;KAC7B;IACD,qBAAqB,GAAG,EAAE,CAAC,IAAI,CAAC;IAEhC,OAAO;QACL,mBAAmB,qBAAA;QACnB,uBAAuB,yBAAA;QACvB,6BAA6B,+BAAA;QAC7B,yBAAyB,2BAAA;QACzB,kBAAkB,oBAAA;QAClB,qBAAqB,uBAAA;QACrB,yBAAyB,2BAAA;QACzB,kBAAkB,oBAAA;QAClB,oBAAoB,sBAAA;QACpB,gBAAgB,kBAAA;KACjB,CAAC;AACJ,CAAC;AAxDD,4CAwDC"}